# 登入API深度阻塞問題修復

## 問題現象

用戶報告：使用兩個不同裝置執行登入各一次，兩邊都有彈出強制登出的提示，但是第二次登入時，會將前者強制登出，但是登入API仍會超時，並且無法正確登入。

## 深度問題分析

### 1. 數據庫事務競爭
**問題**：多個數據庫操作在同一個請求中執行，可能導致鎖競爭
- 撤銷舊token的複雜子查詢
- 設備記錄的查詢和更新
- TokenService中的事務操作

### 2. 複雜子查詢阻塞
**問題位置**：原始的token撤銷邏輯
```sql
UPDATE refresh_tokens SET is_revoked = true 
WHERE access_token_id IN (
    SELECT id FROM access_tokens 
    WHERE user_id = ? AND is_revoked = ? AND expires_at > ?
)
```
**影響**：在高併發時可能導致表鎖定

### 3. 設備記錄競爭條件
**問題**：檢查設備是否存在 → 更新/創建的操作不是原子性的
```go
// 原始代碼的競爭條件
deviceExists := db.Where("user_id = ? AND device_id = ?", user.ID, deviceID).First(&existingDevice).Error == nil
if deviceExists {
    db.Model(&existingDevice).Updates(...)
} else {
    db.Create(&newDevice)
}
```

### 4. WebSocket操作雖然異步但仍有影響
即使WebSocket操作是異步的，但在高併發情況下仍可能影響系統性能。

## 深度修復方案

### 1. 異步Token撤銷
```go
// 修復後：完全異步處理，不阻塞登入流程
go func() {
    log.Printf("🔄 開始異步撤銷用戶 %d 的舊token", user.ID)
    
    // 先獲取要撤銷的access token IDs
    var accessTokenIDs []uint64
    db.Model(&AccessToken{}).
        Where("user_id = ? AND is_revoked = ? AND expires_at > ?", user.ID, false, time.Now()).
        Pluck("id", &accessTokenIDs)
    
    if len(accessTokenIDs) > 0 {
        // 分別撤銷，避免複雜子查詢
        result1 := db.Model(&AccessToken{}).
            Where("id IN ?", accessTokenIDs).
            Update("is_revoked", true)
        
        result2 := db.Model(&RefreshToken{}).
            Where("access_token_id IN ?", accessTokenIDs).
            Update("is_revoked", true)
        
        log.Printf("✅ 撤銷完成 - Access tokens: %d, Refresh tokens: %d", 
            result1.RowsAffected, result2.RowsAffected)
    }
}()
```

### 2. 原子性設備記錄操作
```go
// 使用UPSERT避免競爭條件
result := db.Clauses(clause.OnConflict{
    Columns: []clause.Column{{Name: "user_id"}, {Name: "device_id"}},
    DoUpdates: clause.AssignmentColumns([]string{
        "device_name", "user_agent", "is_active", "last_seen_at", "updated_at",
    }),
}).Create(&deviceRecord)
```

### 3. 數據庫連接優化
```go
// 設置數據庫連接池參數
sqlDB, err := db.DB()
if err == nil {
    sqlDB.SetMaxOpenConns(25)    // 最大開放連接數
    sqlDB.SetMaxIdleConns(5)     // 最大空閒連接數
    sqlDB.SetConnMaxLifetime(5 * time.Minute) // 連接最大生命週期
}
```

### 4. 性能監控
```go
// 添加性能監控
startTime := time.Now()
// ... 登入邏輯 ...
duration := time.Since(startTime)
log.Printf("✅ 登入成功 - 用戶: %s, 耗時: %v", user.UID, duration)

if duration > 5*time.Second {
    log.Printf("⚠️ 登入處理時間過長: %v", duration)
}
```

## 修復效果驗證

### 1. 性能測試
使用提供的測試腳本 `test_login_performance.sh`：
```bash
chmod +x test_login_performance.sh
./test_login_performance.sh
```

### 2. 併發測試
```bash
# 同時發起多個登入請求
for i in {1..10}; do
    curl -X POST http://localhost:8088/api/auth/login \
        -H "Content-Type: application/json" \
        -d '{"uid":"test_user","pwd":"password"}' &
done
wait
```

### 3. 監控指標
- **響應時間**：應 < 1秒
- **成功率**：應 > 99%
- **數據庫連接**：無長時間鎖定
- **WebSocket通知**：正常工作

## 數據庫索引優化

確保以下索引存在以提高查詢性能：

```sql
-- 用戶設備表索引
CREATE INDEX idx_user_devices_user_device ON user_devices(user_id, device_id);
CREATE INDEX idx_user_devices_active ON user_devices(user_id, is_active);

-- Access Token表索引
CREATE INDEX idx_access_tokens_user_revoked_expires ON access_tokens(user_id, is_revoked, expires_at);
CREATE INDEX idx_access_tokens_user_active ON access_tokens(user_id, is_revoked, expires_at);

-- Refresh Token表索引
CREATE INDEX idx_refresh_tokens_access_token ON refresh_tokens(access_token_id);
CREATE INDEX idx_refresh_tokens_revoked ON refresh_tokens(is_revoked);
```

## 故障排除

### 1. 如果登入仍然慢
```bash
# 檢查數據庫慢查詢
SHOW PROCESSLIST;
SHOW FULL PROCESSLIST;

# 檢查鎖定情況
SELECT * FROM INFORMATION_SCHEMA.INNODB_LOCKS;
SELECT * FROM INFORMATION_SCHEMA.INNODB_LOCK_WAITS;
```

### 2. 檢查WebSocket連接
```bash
# 檢查WebSocket連接數
netstat -an | grep :8088 | grep ESTABLISHED | wc -l

# 檢查後端日誌
docker logs lottery-api | grep WebSocket
```

### 3. 數據庫連接池監控
```go
// 在代碼中添加監控
stats := sqlDB.Stats()
log.Printf("DB Stats - Open: %d, InUse: %d, Idle: %d", 
    stats.OpenConnections, stats.InUse, stats.Idle)
```

## 預期結果

修復後的系統應該具有：

1. **快速響應**：登入API響應時間 < 1秒
2. **高併發支持**：支持多用戶同時登入
3. **穩定性**：無數據庫鎖定問題
4. **功能完整**：WebSocket強制登出正常工作
5. **可監控**：詳細的性能日誌

## 長期優化建議

1. **Redis緩存**：將token狀態緩存到Redis
2. **連接池監控**：實時監控數據庫連接狀態
3. **異步處理**：更多操作改為異步處理
4. **負載均衡**：支持多實例部署
5. **監控告警**：設置性能監控和告警

## 總結

通過以上深度修復，解決了：
- ✅ 數據庫事務競爭問題
- ✅ 複雜子查詢阻塞問題  
- ✅ 設備記錄競爭條件
- ✅ 性能監控和優化
- ✅ 連接池配置優化

現在系統應該能夠快速、穩定地處理登入請求，即使在高併發情況下也不會出現阻塞問題。
