package database

import (
	"lottery/utils/snowflake"
	"reflect"

	"gorm.io/gorm"
)

type SnowflakePlugin struct {
	snowflake *snowflake.Snowflake
}

func NewSnowflakePlugin() (*SnowflakePlugin, error) {
	sf, err := snowflake.NewSnowflake()
	if err != nil {
		return nil, err
	}

	return &SnowflakePlugin{
		snowflake: sf,
	}, nil
}

func (p *SnowflakePlugin) Name() string {
	return "snowflake_plugin"
}

func (p *SnowflakePlugin) Initialize(db *gorm.DB) error {
	db.Callback().Create().Before("gorm:create").Register("snowflake:before_create", func(db *gorm.DB) {
		if db.Statement.Schema != nil {
			val := db.Statement.ReflectValue

			switch val.Kind() {
			case reflect.Ptr:
				val = val.Elem()
				idField := val.FieldByName("ID")
				if idField.Type() == reflect.TypeOf(uint64(0)) && idField.Uint() == 0 {
					idField.SetUint(p.snowflake.Generate())
				}
			case reflect.Struct:
				idField := val.FieldByName("ID")
				if !idField.IsValid() {
					return
				}
				if idField.Type() == reflect.TypeOf(uint64(0)) && idField.Uint() == 0 {
					idField.SetUint(p.snowflake.Generate())
				}
			case reflect.Map:
				if !(val.MapIndex(reflect.ValueOf("id")) == reflect.Value{}) && !val.MapIndex(reflect.ValueOf("id")).IsValid() {
					val.SetMapIndex(reflect.ValueOf("id"), reflect.ValueOf(p.snowflake.Generate()))
				}
			}
		}
	})
	return nil
}
