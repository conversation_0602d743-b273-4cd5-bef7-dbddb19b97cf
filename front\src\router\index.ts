import { route } from 'quasar/wrappers';
import {
  createM<PERSON>oryHistory,
  createRouter,
  createWebHashHistory,
  createWebHistory,
  NavigationGuardNext,
  RouteLocationNormalizedGeneric,
} from 'vue-router';
import { useAuthStore } from 'src/stores/auth';
import type { AuthStore } from 'src/stores/auth';

import routes from './routes';

/*
 * If not building with SSR mode, you can
 * directly export the Router instantiation;
 *
 * The function below can be async too; either use
 * async/await or return a Promise which resolves
 * with the Router instance.
 */

export default route(function (/* { store, ssrContext } */) {
  const createHistory = process.env.SERVER
    ? createMemoryHistory
    : process.env.VUE_ROUTER_MODE === 'history'
    ? createWebHistory
    : createWebHashHistory;

  const Router = createRouter({
    scrollBehavior: () => ({ left: 0, top: 0 }),
    routes,

    // Leave this as is and make changes in quasar.conf.js instead!
    // quasar.conf.js -> build -> vueRouterMode
    // quasar.conf.js -> build -> publicPath
    history: createHistory(process.env.VUE_ROUTER_BASE),
  });

  // 檢查是否需要身份驗證
  const checkRequiresAuth = (to: RouteLocationNormalizedGeneric) => {
    return to.matched.some((record) => record.meta.requiresAuth);
  };

  // 檢查是否需要管理員權限
  const checkRequiresAdmin = (to: RouteLocationNormalizedGeneric) => {
    return to.matched.some((record) => record.meta.requiresAdmin);
  };

  // 導向登入頁面
  const redirectToLogin = (
    to: RouteLocationNormalizedGeneric,
    next: NavigationGuardNext
  ) => {
    next({ path: '/login', query: { redirect: to.fullPath } });
  };

  // 初始化 auth store
  const initializeAuth = (authStore: AuthStore) => {
    if (!authStore.accessToken) {
      authStore.initializeFromStorage();
    }
  };

  // 驗證管理員權限（異步版本，會重新驗證用戶資料）
  const validateAdminAccess = async (
    authStore: AuthStore,
    next: NavigationGuardNext
  ): Promise<boolean> => {
    // 確保用戶資料是最新的
    const isValid = await authStore.validateToken();
    if (!isValid) {
      next({ path: '/login' });
      return false;
    }

    // 檢查管理員權限
    if (!authStore.isAdmin()) {
      next({ path: '/' });
      return false;
    }
    return true;
  };

  Router.beforeEach(async (to, _from, next) => {
    const authStore = useAuthStore();
    const requiresAuth = checkRequiresAuth(to);
    const requiresAdmin = checkRequiresAdmin(to);

    initializeAuth(authStore);

    // 確認是否需要身份驗證
    if (requiresAuth) {
      // 使用新的認證檢查方法（包含後端驗證）
      const isAuthenticated = await authStore.checkAuthentication();

      if (!isAuthenticated) {
        redirectToLogin(to, next);
        return;
      }
    }

    // 確認是否需要管理員權限
    if (requiresAdmin) {
      // 先確保用戶已認證
      const isAuthenticated = await authStore.checkAuthentication();
      if (!isAuthenticated) {
        redirectToLogin(to, next);
        return;
      }

      // 驗證管理員權限（需要重新獲取用戶資料）
      if (!await validateAdminAccess(authStore, next)) {
        return;
      }
    }

    next();
  });

  return Router;
});
