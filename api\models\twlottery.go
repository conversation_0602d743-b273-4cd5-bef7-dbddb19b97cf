package models

import "encoding/json"

var TaiwanLotteryRequestUrl = map[LottoType]string{
	SuperLotto638: "https://api.taiwanlottery.com/TLCAPIWeB/Lottery/SuperLotto638Result",
	Lotto649:      "https://api.taiwanlottery.com/TLCAPIWeB/Lottery/Lotto649Result",
	Daily539:      "https://api.taiwanlottery.com/TLCAPIWeB/Lottery/Daily539Result",
	ThreeStar:     "https://api.taiwanlottery.com/TLCAPIWeB/Lottery/3DResult",
	FourStar:      "https://api.taiwanlottery.com/TLCAPIWeB/Lottery/4DResult",
	Lotto39:       "https://api.taiwanlottery.com/TLCAPIWeB/Lottery/39M5Result",
	Lotto49:       "https://api.taiwanlottery.com/TLCAPIWeB/Lottery/49M6Result",
}

type TaiwanLotteryRequestParams struct {
	Period   int    `json:"period" url:"period,omitempty"` // 110000001
	Month    string `json:"month" url:"month,omitempty"`   // 2021-01
	PageNum  int    `json:"pageNum" url:"pageNum"`         // default: 1
	PageSize int    `json:"pageSize" url:"pageSize"`       // default: 50
}

func (p *TaiwanLotteryRequestParams) MarshalJSON() ([]byte, error) {
	return json.Marshal(map[string]interface{}{
		"period":   p.Period,
		"month":    p.Month,
		"pageNum":  p.PageNum,
		"pageSize": p.PageSize,
	})
}

type LottoResponse interface {
	GetLottoResults() []TaiwanLottoRes
}

type TaiwanLottoRes struct {
	Period           int         `json:"period"`
	LotteryDate      string      `json:"lotteryDate"`
	DrawNumberSize   NumberArray `json:"drawNumberSize"`
	DrawNumberAppear NumberArray `json:"drawNumberAppear"`
}

type SuperLottoResponse struct {
	RtCode  int     `json:"rtCode"`
	RtMsg   *string `json:"rtMsg"`
	Content struct {
		TotalSize        int              `json:"totalSize"`
		SuperLotto638Res []TaiwanLottoRes `json:"superLotto638Res"`
	}
}

func (r *SuperLottoResponse) GetLottoResults() []TaiwanLottoRes {
	return r.Content.SuperLotto638Res
}

type Lotto649Response struct {
	RtCode  int     `json:"rtCode"`
	RtMsg   *string `json:"rtMsg"`
	Content struct {
		TotalSize   int              `json:"totalSize"`
		Lotto649Res []TaiwanLottoRes `json:"lotto649Res"`
	}
}

func (r *Lotto649Response) GetLottoResults() []TaiwanLottoRes {
	return r.Content.Lotto649Res
}

type Daily539Response struct {
	RtCode  int     `json:"rtCode"`
	RtMsg   *string `json:"rtMsg"`
	Content struct {
		TotalSize   int              `json:"totalSize"`
		Daily539Res []TaiwanLottoRes `json:"daily539Res"`
	}
}

func (r *Daily539Response) GetLottoResults() []TaiwanLottoRes {
	return r.Content.Daily539Res
}
