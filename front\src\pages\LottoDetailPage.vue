<template>
  <q-page class="lotto-detail-page">
    <!-- 頁面標題和導航 -->
    <div class="page-header q-pa-md">
      <div class="row items-center">
        <div class="col-auto">
          <q-btn
            icon="arrow_back"
            flat
            round
            size="lg"
            color="primary"
            @click="goBack"
            class="back-btn"
          />
        </div>
        <div class="col text-center">
          <h4 class="text-h4 text-weight-bold q-my-none page-title">
            {{ lottoTypeName }}開獎記錄
          </h4>
        </div>
        <div class="col-auto">
          <!-- 空白區域保持對稱 -->
          <div style="width: 48px; height: 48px;"></div>
        </div>
      </div>
    </div>

    <!-- 懸浮回到頂部按鈕 -->
    <q-btn
      fab
      icon="keyboard_arrow_up"
      color="primary"
      class="scroll-top-fab"
      :class="{ 'show': showScrollTop }"
      @click="scrollToTop"
    />

    <!-- 開獎結果列表 -->
    <div class="results-container q-pa-md">
      <!-- 載入中狀態 -->
      <div v-if="loading && items.length === 0" class="text-center q-py-xl">
        <q-spinner-dots size="50px" color="primary" />
        <div class="text-h6 q-mt-md">載入中...</div>
      </div>

      <!-- 開獎結果卡片 -->
      <div v-else>
        <q-card
          v-for="item in items"
          :key="item.period"
          class="result-card q-mb-md"
        >
          <q-card-section class="q-py-md q-px-lg">
            <div class="row q-gutter-y-md">
              <!-- 期號和日期 -->
              <div class="col-12 col-sm-4 draw-title text-center">
                <div class="text-period">第 {{ item.period }} 期</div>
                <div class="text-draw-date">{{ item.draw_date }}</div>
              </div>

              <!-- 開獎號碼 -->
              <div class="col-12 col-sm-8 self-center">
                <div class="row justify-center">
                  <template v-for="number in item.draw_number_size" :key="number">
                    <div class="col-auto">
                      <div class="ball">
                        {{ paddingZero(number) }}
                      </div>
                    </div>
                  </template>
                  <div class="col-auto" v-if="item.special_number">
                    <div class="ball special-number">
                      {{ paddingZero(item.special_number) }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </q-card-section>
        </q-card>

        <!-- 載入更多指示器 -->
        <div v-if="loading && items.length > 0" class="text-center q-py-md">
          <q-spinner-dots size="40px" color="primary" />
          <div class="text-subtitle1 q-mt-sm">載入更多...</div>
        </div>

        <!-- 沒有更多資料 -->
        <div v-if="!hasMore && items.length > 0" class="text-center q-py-md">
          <q-icon name="check_circle" size="40px" color="positive" />
          <div class="text-subtitle1 q-mt-sm text-grey-7">已載入所有資料</div>
        </div>

        <!-- 無資料狀態 -->
        <div v-if="!loading && items.length === 0" class="text-center q-py-xl">
          <q-icon name="info" size="60px" color="grey-5" />
          <div class="text-h6 q-mt-md text-grey-7">暫無開獎資料</div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { LOTTO_API } from '@/api';
import { LottoItem } from '@/api/modules/lotto';
import { paddingZero } from '@/utils';
import { useVersionCheck } from '@/composables/useVersionCheck';

defineOptions({
  name: 'LottoDetailPage',
});

const router = useRouter();
const route = useRoute();

// 使用版本檢查
useVersionCheck();

const drawType = ref('');
const items = ref<LottoItem[]>([]);
const loading = ref(false);
const hasMore = ref(true);
const showScrollTop = ref(false);
const pageSize = 20;
const currentOffset = ref(0);

// 獲取彩種名稱
const lottoTypeName = computed(() => {
  switch (drawType.value) {
    case 'super_lotto638':
      return '威力彩';
    case 'lotto649':
      return '大樂透';
    case 'daily539':
      return '今彩539';
    case 'lotto_hk':
      return '六合彩';
    case 'ca_lotto':
      return '加州天天樂';
    default:
      return '';
  }
});

// 獲取開獎結果
const fetchLottoResults = async (append = false) => {
  if (loading.value) return;

  try {
    loading.value = true;

    // 使用currentOffset來追蹤已載入的資料數量
    const offset = append ? currentOffset.value : 0;

    const { data } = await LOTTO_API.getLottoList({
      draw_type: drawType.value,
      limit: pageSize,
      offset: offset,
      ascending: false, // 最新的在前面
    });

    if (append) {
      items.value.push(...data);
      currentOffset.value += data.length;
    } else {
      items.value = data;
      currentOffset.value = data.length;
    }

    // 如果返回的資料少於頁面大小，表示沒有更多資料了
    hasMore.value = data.length === pageSize;

  } catch (error) {
    console.error('獲取開獎結果失敗:', error);
  } finally {
    loading.value = false;
  }
};

// 載入更多資料
const loadMore = () => {
  if (!loading.value && hasMore.value) {
    fetchLottoResults(true);
  }
};

// 滾動事件處理
const handleScroll = () => {
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  const windowHeight = window.innerHeight;
  const documentHeight = document.documentElement.scrollHeight;

  // 顯示/隱藏回到頂部按鈕
  showScrollTop.value = scrollTop > 300;

  // 檢查是否需要載入更多資料（增加防抖機制）
  if (scrollTop + windowHeight >= documentHeight - 200 && !loading.value && hasMore.value) {
    loadMore();
  }
};

// 回到頂部
const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  });
};

// 返回上一頁
const goBack = () => {
  router.push('/lotto-results');
};

// 初始化數據的函數
const initializeData = () => {
  // 重置狀態
  items.value = [];
  currentOffset.value = 0;
  hasMore.value = true;
  loading.value = false;

  fetchLottoResults();
};

// 監聽路由參數變化
watch(() => route.params.drawType, (newDrawType) => {
  if (newDrawType && newDrawType !== drawType.value) {
    drawType.value = newDrawType as string;
    initializeData();
  }
}, { immediate: true });

onMounted(() => {
  const routeDrawType = route.params.drawType as string;

  if (!routeDrawType) {
    router.push('/lotto-results');
    return;
  }

  drawType.value = routeDrawType;
  window.addEventListener('scroll', handleScroll);
});

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll);
});
</script>

<style lang="scss" scoped>
.lotto-detail-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.page-header {
  background-color: #fff;
  border-bottom: 2px solid #e9ecef;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .page-title {
    color: #2c3e50;
    font-size: 1.8rem;
  }

  .back-btn {
    color: #3460f2;

    &:hover {
      background-color: rgba(52, 96, 242, 0.1);
    }
  }
}

.scroll-top-fab {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1000;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.3s ease;

  &.show {
    opacity: 1;
    transform: scale(1);
  }
}

.results-container {
  max-width: 1200px;
  margin: 0 auto;
}

.result-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  &::before {
    content: '';
    width: 5px;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    border-top-left-radius: 12px;
    border-bottom-left-radius: 12px;
    background: linear-gradient(180deg, #da8359 0%, #ff6b35 100%);
  }
}

.draw-title {
  .text-period {
    font-size: 2rem;
    font-weight: bold;
    color: #2c3e50;
  }

  .text-draw-date {
    color: #666;
    font-size: 1.4rem;
    margin-top: 0.5rem;
  }
}

// 響應式設計
@media (max-width: 768px) {
  .page-header {
    .page-title {
      font-size: 1.5rem;
    }
  }

  .draw-title {
    margin-bottom: 1rem;

    .text-period {
      font-size: 1.7rem;
    }

    .text-draw-date {
      font-size: 1.5rem;
    }
  }

  .scroll-top-fab {
    bottom: 20px;
    right: 20px;
  }
}

@media (max-width: 480px) {
  .page-header {
    .page-title {
      font-size: 1.3rem;
    }
  }

  .draw-title {
    .text-period {
      font-size: 1.6rem;
    }

    .text-draw-date {
      font-size: 1.2rem;
    }
  }

  .scroll-top-fab {
    bottom: 15px;
    right: 15px;
  }
}
</style>
