/**
 * 日期格式化工具函數
 * 提供各種日期格式化選項，支援台灣地區格式
 */

export interface DateFormatOptions {
  /** 是否顯示時間 */
  showTime?: boolean;
  /** 是否使用 12 小時制 */
  hour12?: boolean;
  /** 是否顯示秒數 */
  showSeconds?: boolean;
  /** 日期分隔符 */
  dateSeparator?: string;
  /** 時間分隔符 */
  timeSeparator?: string;
  /** 自定義格式 */
  customFormat?: 'short' | 'medium' | 'long' | 'full';
  /** 語言地區 */
  locale?: string;
}

/**
 * 格式化日期為台灣地區格式
 * @param date 日期字串、Date 物件或時間戳
 * @param options 格式化選項
 * @returns 格式化後的日期字串，如果日期無效則返回空字串
 */
export function formatDate(
  date: string | Date | number | null | undefined,
  options: DateFormatOptions = {}
): string {
  if (!date) return '';

  try {
    const dateObj = new Date(date);

    // 檢查日期是否有效
    if (isNaN(dateObj.getTime())) {
      return '';
    }

    const {
      showTime = false,
      hour12 = false,
      showSeconds = false,
      dateSeparator = '/',
      timeSeparator = ':',
      customFormat,
      locale = 'zh-TW'
    } = options;

    // 如果有自定義格式，使用 toLocaleDateString 或 toLocaleString
    if (customFormat) {
      const formatOptions: Intl.DateTimeFormatOptions = {};

      switch (customFormat) {
        case 'short':
          formatOptions.dateStyle = 'short';
          if (showTime) formatOptions.timeStyle = 'short';
          break;
        case 'medium':
          formatOptions.dateStyle = 'medium';
          if (showTime) formatOptions.timeStyle = 'medium';
          break;
        case 'long':
          formatOptions.dateStyle = 'long';
          if (showTime) formatOptions.timeStyle = 'long';
          break;
        case 'full':
          formatOptions.dateStyle = 'full';
          if (showTime) formatOptions.timeStyle = 'full';
          break;
      }

      return showTime
        ? dateObj.toLocaleString(locale, formatOptions)
        : dateObj.toLocaleDateString(locale, formatOptions);
    }

    // 自定義格式化
    const year = dateObj.getFullYear();
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    const day = String(dateObj.getDate()).padStart(2, '0');

    let result = `${year}${dateSeparator}${month}${dateSeparator}${day}`;

    if (showTime) {
      let hours = dateObj.getHours();
      const minutes = String(dateObj.getMinutes()).padStart(2, '0');
      const seconds = String(dateObj.getSeconds()).padStart(2, '0');

      let timeStr = '';

      if (hour12) {
        const ampm = hours >= 12 ? '下午' : '上午';
        hours = hours % 12;
        if (hours === 0) hours = 12;
        timeStr = `${ampm} ${String(hours).padStart(2, '0')}${timeSeparator}${minutes}`;
      } else {
        timeStr = `${String(hours).padStart(2, '0')}${timeSeparator}${minutes}`;
      }

      if (showSeconds) {
        timeStr += `${timeSeparator}${seconds}`;
      }

      result += ` ${timeStr}`;
    }

    return result;
  } catch (error) {
    console.error('Date formatting error:', error);
    return '';
  }
}

/**
 * 格式化日期為僅顯示年月日的格式
 * @param date 日期字串、Date 物件或時間戳
 * @param separator 分隔符，預設為 '/'
 * @returns 格式化後的日期字串 (YYYY/MM/DD)
 */
export function formatDateOnly(
  date: string | Date | number | null | undefined,
  separator = '/'
): string {
  return formatDate(date, {
    showTime: false,
    dateSeparator: separator
  });
}

/**
 * 格式化日期時間為完整格式
 * @param date 日期字串、Date 物件或時間戳
 * @param hour12 是否使用 12 小時制，預設為 false
 * @param showSeconds 是否顯示秒數，預設為 false
 * @returns 格式化後的日期時間字串 (YYYY/MM/DD HH:mm 或 YYYY/MM/DD 上午/下午 HH:mm)
 */
export function formatDateTime(
  date: string | Date | number | null | undefined,
  hour12 = false,
  showSeconds = false
): string {
  return formatDate(date, {
    showTime: true,
    hour12,
    showSeconds
  });
}

/**
 * 格式化日期為台灣標準格式 (使用 toLocaleDateString)
 * @param date 日期字串、Date 物件或時間戳
 * @returns 格式化後的日期字串
 */
export function formatDateTW(
  date: string | Date | number | null | undefined
): string {
  if (!date) return '';

  try {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return '';

    return dateObj.toLocaleDateString('zh-TW', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
  } catch (error) {
    console.error('Date formatting error:', error);
    return '';
  }
}

/**
 * 格式化日期時間為台灣標準格式 (使用 toLocaleString)
 * @param date 日期字串、Date 物件或時間戳
 * @param hour12 是否使用 12 小時制，預設為 false
 * @returns 格式化後的日期時間字串
 */
export function formatDateTimeTW(
  date: string | Date | number | null | undefined,
  hour12 = false
): string {
  if (!date) return '';

  try {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return '';

    return dateObj.toLocaleString('zh-TW', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12,
    });
  } catch (error) {
    console.error('Date formatting error:', error);
    return '';
  }
}

/**
 * 格式化相對時間 (例如：2小時前、3天前)
 * @param date 日期字串、Date 物件或時間戳
 * @returns 相對時間字串
 */
export function formatRelativeTime(
  date: string | Date | number | null | undefined
): string {
  if (!date) return '';

  try {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return '';

    const now = new Date();
    const diffMs = now.getTime() - dateObj.getTime();
    const diffSeconds = Math.floor(diffMs / 1000);
    const diffMinutes = Math.floor(diffSeconds / 60);
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);
    const diffMonths = Math.floor(diffDays / 30);
    const diffYears = Math.floor(diffDays / 365);

    if (diffSeconds < 60) {
      return '剛剛';
    } else if (diffMinutes < 60) {
      return `${diffMinutes}分鐘前`;
    } else if (diffHours < 24) {
      return `${diffHours}小時前`;
    } else if (diffDays < 30) {
      return `${diffDays}天前`;
    } else if (diffMonths < 12) {
      return `${diffMonths}個月前`;
    } else {
      return `${diffYears}年前`;
    }
  } catch (error) {
    console.error('Relative time formatting error:', error);
    return '';
  }
}

/**
 * 檢查日期是否有效
 * @param date 日期字串、Date 物件或時間戳
 * @returns 是否為有效日期
 */
export function isValidDate(
  date: string | Date | number | null | undefined
): boolean {
  if (!date) return false;

  try {
    const dateObj = new Date(date);
    return !isNaN(dateObj.getTime());
  } catch (error) {
    return false;
  }
}

/**
 * 格式化日期範圍
 * @param startDate 開始日期
 * @param endDate 結束日期
 * @param options 格式化選項
 * @returns 格式化後的日期範圍字串
 */
export function formatDateRange(
  startDate: string | Date | number | null | undefined,
  endDate: string | Date | number | null | undefined,
  options: DateFormatOptions = {}
): string {
  const start = formatDate(startDate, options);
  const end = formatDate(endDate, options);

  if (!start && !end) return '';
  if (!start) return `至 ${end}`;
  if (!end) return `${start} 至今`;

  return `${start} 至 ${end}`;
}

/**
 * 格式化為 ISO 日期字串 (YYYY-MM-DD)
 * @param date 日期字串、Date 物件或時間戳
 * @returns ISO 格式的日期字串
 */
export function formatDateISO(
  date: string | Date | number | null | undefined
): string {
  if (!date) return '';

  try {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return '';

    return dateObj.toISOString().split('T')[0];
  } catch (error) {
    console.error('ISO date formatting error:', error);
    return '';
  }
}

/**
 * 格式化為時間字串 (HH:mm 或 HH:mm:ss)
 * @param date 日期字串、Date 物件或時間戳
 * @param showSeconds 是否顯示秒數
 * @param hour12 是否使用 12 小時制
 * @returns 時間字串
 */
export function formatTimeOnly(
  date: string | Date | number | null | undefined,
  showSeconds = false,
  hour12 = false
): string {
  if (!date) return '';

  try {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return '';

    const options: Intl.DateTimeFormatOptions = {
      hour: '2-digit',
      minute: '2-digit',
      hour12,
    };

    if (showSeconds) {
      options.second = '2-digit';
    }

    return dateObj.toLocaleTimeString('zh-TW', options);
  } catch (error) {
    console.error('Time formatting error:', error);
    return '';
  }
}

/**
 * 計算兩個日期之間的天數差
 * @param startDate 開始日期
 * @param endDate 結束日期
 * @returns 天數差 (正數表示 endDate 在 startDate 之後)
 */
export function getDaysDifference(
  startDate: string | Date | number,
  endDate: string | Date | number
): number {
  try {
    const start = new Date(startDate);
    const end = new Date(endDate);

    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return 0;
    }

    const diffTime = end.getTime() - start.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  } catch (error) {
    console.error('Days difference calculation error:', error);
    return 0;
  }
}

/**
 * 檢查日期是否為今天
 * @param date 日期字串、Date 物件或時間戳
 * @returns 是否為今天
 */
export function isToday(
  date: string | Date | number | null | undefined
): boolean {
  if (!date) return false;

  try {
    const dateObj = new Date(date);
    const today = new Date();

    return dateObj.toDateString() === today.toDateString();
  } catch (error) {
    return false;
  }
}

/**
 * 檢查日期是否為昨天
 * @param date 日期字串、Date 物件或時間戳
 * @returns 是否為昨天
 */
export function isYesterday(
  date: string | Date | number | null | undefined
): boolean {
  if (!date) return false;

  try {
    const dateObj = new Date(date);
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);

    return dateObj.toDateString() === yesterday.toDateString();
  } catch (error) {
    return false;
  }
}

/**
 * 格式化智能日期 (今天顯示時間，其他顯示日期)
 * @param date 日期字串、Date 物件或時間戳
 * @returns 智能格式化的日期字串
 */
export function formatSmartDate(
  date: string | Date | number | null | undefined
): string {
  if (!date) return '';

  try {
    if (isToday(date)) {
      return `今天 ${formatTimeOnly(date)}`;
    } else if (isYesterday(date)) {
      return `昨天 ${formatTimeOnly(date)}`;
    } else {
      return formatDateTW(date);
    }
  } catch (error) {
    console.error('Smart date formatting error:', error);
    return '';
  }
}
