package utils

import (
	"fmt"
	"log"
	"os"
	"time"
)

type ErrorMsg struct {
	Msg   string `json:"message"`
	Error string `json:"error"`
}

func init() {
	// 建立log folder
	if _, err := os.Stat("log"); os.IsNotExist(err) {
		os.Mkdir("log", 0755)
	}
}

func ErrorLog(e ErrorMsg) {
	if e.Error == "" && e.Msg == "" {
		return
	}

	// 依照日期建立log file
	logPath := fmt.Sprintf("log/%s.log", time.Now().Format("2006-01-02"))

	file, err := os.OpenFile(logPath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		log.Printf("error opening file: %v\n", err)
	}
	defer file.Close()

	log.SetOutput(file)
	if e.Error != "" {
		log.Printf("error: %s\n", e.Error)
	}
	if e.Msg != "" {
		log.Printf("error message: %s\n", e.Msg)
	}
	fmt.Println(e)
}
