# 版本檢查策略優化

## 修改概述

根據需求，我們將版本檢查策略從定時檢查改為按需檢查，並簡化了更新流程。

## 主要變更

### 1. App.vue 修改

**移除的功能：**
- 用戶活動追蹤 (userActivityTracker)
- 複雜的更新對話框
- 定時版本檢查
- 頁面可見性檢查

**新增的功能：**
- 路由切換時的版本檢查
- 使用 Quasar Notify 顯示更新通知
- 簡化的靜默更新流程

**關鍵變更：**
```typescript
// 路由切換時檢查版本
router.afterEach(() => {
  this.checkVersionOnRouteChange();
});

// 使用 Quasar Notify 顯示通知
showUpdatingNotification() {
  Notify.create({
    message: '正在更新到最新版本...',
    icon: 'cloud_download',
    color: 'primary',
    position: 'top-right',
    timeout: 2000,
    spinner: true
  });
}
```

### 2. versionChecker.ts 修改

**移除的功能：**
- `startVersionCheck()` 方法
- `checkInterval` 屬性
- 定時檢查邏輯
- `checkVersion()` 和 `checkVersionFile()` 方法
- `handleNewVersion()` 方法

**保留的功能：**
- `manualVersionCheck()` - 手動版本檢查
- `performUpdate()` - 執行更新
- 緩存清理功能

### 3. 新增 useVersionCheck composable

創建了 `front/src/composables/useVersionCheck.ts`：

```typescript
export function useVersionCheck() {
  onMounted(async () => {
    // 在開發環境中跳過
    if (process.env.DEV) {
      return;
    }

    // 檢查是否為第一次載入
    const isFirstLoad = !sessionStorage.getItem('app-session-started');
    if (isFirstLoad) {
      return;
    }

    try {
      const hasUpdate = await versionChecker.manualVersionCheck();
      if (hasUpdate) {
        // 發送版本更新事件
        const event = new CustomEvent('version-update-detected', {
          detail: {
            source: 'component-mount',
            timestamp: Date.now()
          }
        });
        window.dispatchEvent(event);
      }
    } catch (error) {
      console.error('組件版本檢查失敗:', error);
    }
  });
}
```

### 4. 頁面組件修改

在以下主要頁面中添加了版本檢查：
- `LottoResultsPage.vue` - 彩票結果頁面
- `LottoDetailPage.vue` - 彩票詳情頁面
- `ProfilePage.vue` - 個人資料頁面
- `BallFollowPage.vue` - 球號跟蹤頁面
- `TailPage.vue` - 尾數分析頁面

使用方式：
```typescript
import { useVersionCheck } from '@/composables/useVersionCheck';

// 在 setup 函數中調用
useVersionCheck();
```

## 新的版本檢查流程

1. **路由切換觸發**：用戶切換路由時，App.vue 會檢查是否有新版本
2. **組件渲染觸發**：主要頁面組件掛載時會檢查版本
3. **靜默更新**：發現新版本時直接在背景更新，不彈出對話框
4. **通知顯示**：使用 Quasar Notify 在右上角顯示更新進度和完成通知

## 優勢

1. **性能提升**：不再有定時檢查，減少不必要的網絡請求
2. **用戶體驗**：無干擾的背景更新，使用簡潔的通知
3. **代碼簡化**：移除複雜的用戶活動追蹤和對話框邏輯
4. **按需檢查**：只在用戶實際使用應用時檢查版本

## 開發環境

在開發環境中，所有版本檢查功能都會被跳過，避免干擾開發工作。

## 注意事項

- 確保 Quasar Notify 插件已正確配置
- 版本檢查依賴於 sessionStorage 來判斷是否為首次載入
- Service Worker 相關的更新邏輯保持不變
