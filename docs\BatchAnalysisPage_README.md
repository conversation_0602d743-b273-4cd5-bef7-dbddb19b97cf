# BatchAnalysisPage 報表產生功能說明

## 功能概述

BatchAnalysisPage 是一個批量分析報表產生頁面，可以根據不同的分析方法（版路分析、尾數分析、綜合分析）對過去的開獎數據進行批量計算，並生成Excel報表。

## 主要功能

### 1. 分析方法選擇
- **版路分析 (ball-follow)**: 基於號碼拖牌組合的分析
- **尾數分析 (tail)**: 基於號碼尾數的分析  
- **綜合分析 (pattern)**: 結合版路和尾數分析

### 2. 彩種支持
- 威力彩 (super_lotto638)
- 大樂透 (lotto649)
- 今彩539 (daily539)
- 六合彩 (lotto_hk)

### 3. 參數設定
根據選擇的分析方法，會顯示對應的參數設定：

#### 版路分析參數
- 拖牌組合：第一組、第二組、第三組號碼數量
- 推算期數：用於分析的歷史期數
- 拖牌區間：搜尋範圍限制
- 預測期數：預測未來幾期

#### 尾數分析參數
- 拖牌組合：第一組、第二組、第三組尾數數量
- 推算期數：用於分析的歷史期數
- 拖牌區間：搜尋範圍限制
- 預測期數：預測未來幾期

#### 綜合分析參數
- 版路拖牌組合：三組號碼組合
- 尾數拖牌組合：三組尾數組合
- 推算期數：用於分析的歷史期數
- 預測期數：預測未來幾期

## 報表格式

### Excel報表結構
根據報表格式.jpg的要求，報表包含以下內容：

1. **標題行**：顯示分析方法和彩種
2. **表頭**：期數、日期、預測號碼、實際號碼、命中號碼、命中數量
3. **數據行**：
   - 每期的分析結果
   - 預測號碼每10個為一列
   - 命中號碼用 `*` 標記（對應原格式的紅字）
   - 包含日期標註

### 特殊功能
- **批量計算**：根據periodNum參數，批量計算過去多期的分析結果
- **命中標註**：預測結果與實際開獎號碼相符時，會特別標記
- **多工作表**：綜合分析會產生多個工作表（版路分析、尾數分析、綜合統計）

## 使用流程

1. **選擇分析方法**：從下拉選單選擇要使用的分析方法
2. **選擇彩種**：選擇要分析的彩票種類
3. **設定參考日期**：選擇分析的截止日期
4. **調整參數**：根據分析方法調整相關參數
5. **開始分析**：點擊「產生報表」按鈕開始批量分析
6. **下載報表**：分析完成後，點擊下載按鈕獲取Excel檔案

## 技術實現

### 核心組件
- `useLotteryAnalysis`: 分析邏輯的composable
- `XLSX`: Excel檔案生成庫
- `LOTTO_API`: 開獎數據API

### 數據流程
1. 獲取歷史開獎數據
2. 對每期數據進行分析計算
3. 收集分析結果和預測號碼
4. 比對預測結果與實際開獎
5. 生成Excel報表並下載

### 批量分析邏輯
- 根據參考日期和期數參數，獲取要分析的期數列表
- 對每一期，使用該期之前的歷史數據進行分析
- 獲取該期的實際開獎結果進行比對
- 記錄預測準確度和命中情況

## 注意事項

1. **記憶體使用**：大量數據分析可能消耗較多記憶體
2. **計算時間**：批量分析需要時間，請耐心等待
3. **數據準確性**：確保選擇的參考日期有足夠的歷史數據
4. **檔案格式**：生成的Excel檔案包含完整的分析結果和統計資訊

## 檔案命名規則

生成的Excel檔案命名格式：`{分析方法}_{彩種}_{日期}.xlsx`

例如：`版路分析_威力彩_20241212.xlsx`
