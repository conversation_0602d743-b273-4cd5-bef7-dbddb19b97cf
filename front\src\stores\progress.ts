import { defineStore } from 'pinia';
import { ref } from 'vue';
import type { ProgressInfo, WarningInfo } from '@/models/types';

export const useProgressStore = defineStore('progress', {
  state: () => ({
    progress: ref<number>(0),
    progressMessage: ref<string>(''),
    isCalculating: ref<boolean>(false),
    stage: ref<string>('preparation'),
    estimatedTime: ref<number>(0),
    startTime: ref<number>(0),
    warnings: ref<(WarningInfo & { timestamp: number; id: string })[]>([]),
    memoryUsage: ref<{ usedMB: number; limitMB: number; percentage: number } | null>(null),
  }),
  actions: {
    startCalculating() {
      this.progress = 0;
      this.progressMessage = '準備開始分析...';
      this.isCalculating = true;
      this.stage = 'preparation';
      this.startTime = Date.now();
      this.estimatedTime = 0;
    },
    async updateProgress(info: ProgressInfo) {
      return new Promise<void>((resolve) => {
        requestAnimationFrame(() => {
          if (!info.progress || !info.total) return;

          // 確保進度值在有效範圍內
          const progress = Math.round((info.progress / info.total) * 100);
          const normalizedProgress = Math.max(0, Math.min(progress, 100));

          // 更新所有進度相關狀態
          this.progress = normalizedProgress * 0.01;
          this.stage = info.stage;

          // 計算預估剩餘時間
          if (normalizedProgress > 0 && this.startTime > 0) {
            const elapsed = Date.now() - this.startTime;
            const estimatedTotal = (elapsed / normalizedProgress) * 100;
            this.estimatedTime = Math.max(0, estimatedTotal - elapsed);
          }

          // 根據階段更新消息
          this.progressMessage = this.getStageMessage(info.stage, normalizedProgress);

          resolve();
        });
      });
    },
    getStageMessage(stage: string, progress: number): string {
      switch (stage) {
        case 'preparation':
          return '正在準備分析數據...';
        case 'processing':
          if (progress === 100) {
            return '分析完成，正在整理結果...';
          } else {
            const timeStr = this.estimatedTime > 0 ?
              ` (預估剩餘: ${Math.ceil(this.estimatedTime / 1000)}秒)` : '';
            return `正在分析中：${progress.toFixed(0)}%${timeStr}`;
          }
        case 'finalization':
          return '正在生成最終結果...';
        case 'complete':
          return '分析完成！';
        default:
          return `處理中：${progress.toFixed(0)}%`;
      }
    },
    stopCalculating() {
      this.isCalculating = false;
      this.estimatedTime = 0;
    },
    addWarning(warning: WarningInfo) {
      this.warnings.push({
        ...warning,
        timestamp: Date.now(),
        id: Math.random().toString(36).substring(2, 11)
      });

      // 如果是記憶體警告，更新記憶體使用狀態
      if (warning.usedMB && warning.limitMB && warning.percentage) {
        this.memoryUsage = {
          usedMB: warning.usedMB,
          limitMB: warning.limitMB,
          percentage: warning.percentage
        };
      }
    },
    clearWarnings() {
      this.warnings = [];
      this.memoryUsage = null;
    },
    resetProgress() {
      this.progress = 0;
      this.progressMessage = '';
      this.isCalculating = false;
      this.stage = 'preparation';
      this.estimatedTime = 0;
      this.startTime = 0;
      this.warnings = [];
      this.memoryUsage = null;
    },
  },
});
