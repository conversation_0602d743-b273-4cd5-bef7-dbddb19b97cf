# GetUserDevices 重複聲明問題修正

## 問題描述

在 `api/controllers/auth.go` 和 `api/controllers/device_management.go` 兩個文件中都定義了 `GetUserDevices` 函數，導致編譯時出現 "redeclared" 錯誤。

## 問題原因

```go
// auth.go 中的定義
func GetUserDevices(c *gin.Context) {
    userID := c.Param("id")  // 使用 :id 參數
    // ... 基本的設備列表獲取
}

// device_management.go 中的定義（重複）
func GetUserDevices(c *gin.Context) {
    userIDStr := c.Param("user_id")  // 使用 :user_id 參數
    // ... 增強的設備列表獲取（包含解析後的用戶代理信息）
}
```

## 解決方案

### 1. 重命名函數

將 `device_management.go` 中的函數重命名為 `GetUserDevicesDetailed`，以反映其提供更詳細信息的特性：

```go
// device_management.go
func GetUserDevicesDetailed(c *gin.Context) {
    userIDStr := c.Param("user_id")
    userID, err := strconv.ParseUint(userIDStr, 10, 64)
    // ... 詳細的設備信息處理
}
```

### 2. 更新路由配置

在 `api/routes/api.go` 中更新路由配置：

```go
// 原有路由（保持不變）
user.GET("/:id/devices", GetUserDevices)  // 使用 auth.go 中的函數

// 新增路由
device.GET("/user/:user_id", GetUserDevicesDetailed)  // 使用 device_management.go 中的函數
```

### 3. 函數功能區分

**GetUserDevices** (auth.go):
- 路由：`/api/admin/users/:id/devices`
- 功能：基本的用戶設備列表
- 用途：用戶管理頁面的設備顯示
- 返回：基本設備信息

**GetUserDevicesDetailed** (device_management.go):
- 路由：`/api/admin/devices/user/:user_id`
- 功能：詳細的用戶設備列表
- 用途：設備管理頁面的詳細分析
- 返回：包含解析後用戶代理信息的詳細設備數據

## 修正後的 API 端點

### 用戶管理相關
```
GET /api/admin/users/:id/devices          # 獲取用戶設備列表（基本）
DELETE /api/admin/users/:id/devices/:device_id  # 刪除用戶設備
```

### 設備管理相關
```
GET /api/admin/devices/statistics         # 設備統計信息
POST /api/admin/devices/cleanup           # 清理重複設備
GET /api/admin/devices/duplicates         # 獲取重複設備列表
GET /api/admin/devices/user/:user_id      # 獲取用戶設備列表（詳細）
DELETE /api/admin/devices/:device_id      # 刪除設備
POST /api/admin/devices/merge             # 合併設備
```

## 數據結構差異

### GetUserDevices 返回格式
```json
{
  "message": "獲取設備列表成功",
  "data": [
    {
      "id": 1,
      "user_id": 123,
      "device_id": "abc123...",
      "stable_device_id": "xyz789...",
      "device_name": "Chrome Browser",
      "user_agent": "Mozilla/5.0...",
      "is_active": true,
      "last_seen_at": "2024-01-15T10:30:00Z",
      "confidence": 85,
      "created_at": "2024-01-01T08:00:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  ]
}
```

### GetUserDevicesDetailed 返回格式
```json
{
  "message": "獲取用戶設備列表成功",
  "data": [
    {
      "id": 1,
      "user_id": 123,
      "device_id": "abc123...",
      "stable_device_id": "xyz789...",
      "device_name": "Chrome Browser",
      "user_agent": "Mozilla/5.0...",
      "is_active": true,
      "last_seen_at": "2024-01-15T10:30:00Z",
      "confidence": 85,
      "created_at": "2024-01-01T08:00:00Z",
      "updated_at": "2024-01-15T10:30:00Z",
      "parsed_user_agent": {
        "browser": "Chrome",
        "os": "Windows",
        "device_type": "Desktop"
      }
    }
  ]
}
```

## 使用場景

### 1. 用戶管理頁面
使用 `GetUserDevices` 獲取基本設備信息：
```javascript
// 前端調用
const response = await fetch(`/api/admin/users/${userId}/devices`, {
  headers: { 'Authorization': `Bearer ${token}` }
});
```

### 2. 設備管理頁面
使用 `GetUserDevicesDetailed` 獲取詳細設備信息：
```javascript
// 前端調用
const response = await fetch(`/api/admin/devices/user/${userId}`, {
  headers: { 'Authorization': `Bearer ${token}` }
});
```

### 3. 設備分析和清理
使用設備管理相關的其他端點：
```javascript
// 獲取設備統計
const stats = await fetch('/api/admin/devices/statistics');

// 清理重複設備
const cleanup = await fetch('/api/admin/devices/cleanup', { method: 'POST' });

// 獲取重複設備列表
const duplicates = await fetch('/api/admin/devices/duplicates');
```

## 驗證修正

### 1. 編譯檢查
```bash
cd api
go build .
```
應該不再出現 "redeclared" 錯誤。

### 2. 功能測試
```bash
# 測試基本設備列表
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:8088/api/admin/users/1/devices

# 測試詳細設備列表
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:8088/api/admin/devices/user/1
```

### 3. 路由檢查
確認兩個端點都能正常工作且返回適當的數據格式。

## 總結

通過重命名函數和明確功能分工，我們解決了重複聲明的問題，同時：

1. ✅ **消除編譯錯誤**：不再有函數重複聲明
2. ✅ **功能明確分工**：基本版本 vs 詳細版本
3. ✅ **保持向後兼容**：現有的用戶管理功能不受影響
4. ✅ **增強設備管理**：提供更豐富的設備分析功能

這樣的設計讓代碼結構更清晰，功能職責更明確，同時為未來的擴展提供了良好的基礎。
