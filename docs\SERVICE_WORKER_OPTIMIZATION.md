# Service Worker 智能更新優化

## 問題描述

原有的Service Worker更新機制存在用戶體驗問題：
1. **突然重整頁面**：當系統檢測到更新時直接重整頁面，可能導致用戶遺失正在操作的資料
2. **缺乏用戶控制**：用戶無法選擇更新時機，不知道為什麼頁面被重新整理
3. **操作中斷**：在用戶正在進行重要操作時強制更新，影響用戶體驗

## 優化方案

### 1. 智能更新策略

#### 用戶狀態檢測
- **新會話檢測**：判斷用戶是否剛進入頁面
- **活動狀態追蹤**：監控用戶是否正在活躍使用
- **頁面可見性管理**：追蹤頁面是否從隱藏狀態恢復

#### 更新策略分類
1. **自動更新**：用戶重新進入頁面時
2. **對話框更新**：用戶正在活躍使用時
3. **延遲更新**：用戶不活躍時延遲執行

### 2. 核心功能實現

#### 用戶活動追蹤器 (userActivityTracker)
```typescript
const userActivityTracker = {
  isUserActive: false,
  lastActivityTime: Date.now(),
  activityTimeout: 30 * 1000, // 30秒無活動視為非活躍
  
  // 監聽用戶活動事件
  init() {
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    events.forEach(event => {
      document.addEventListener(event, this.updateActivity.bind(this), true);
    });
  },
  
  // 檢查是否為新會話
  isNewSession(): boolean {
    const wasHidden = sessionStorage.getItem('page-was-hidden') === 'true';
    const isFirstLoad = !sessionStorage.getItem('app-session-started');
    return isFirstLoad || wasHidden;
  }
}
```

#### 頁面可見性管理器 (pageVisibilityManager)
```typescript
const pageVisibilityManager = {
  init() {
    // 監聽頁面可見性變化
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        sessionStorage.setItem('page-was-hidden', 'true');
      } else {
        // 頁面重新可見時檢查待處理更新
        this.checkForPendingUpdates();
      }
    });
  },
  
  // 執行靜默更新
  performSilentUpdate() {
    this.showSilentUpdateNotification();
    setTimeout(() => {
      window.location.reload();
    }, 2000);
  }
}
```

#### 智能更新管理器 (smartUpdateManager)
```typescript
const smartUpdateManager = {
  handleSmartUpdate() {
    const isNewSession = userActivityTracker.isNewSession();
    const isUserActive = userActivityTracker.isUserActive;
    
    if (isNewSession) {
      // 新會話：自動更新
      pageVisibilityManager.performSilentUpdate();
    } else if (isUserActive) {
      // 用戶活躍：顯示對話框
      this.showSmartUpdateDialog();
    } else {
      // 用戶不活躍：延遲後自動更新
      setTimeout(() => {
        if (!userActivityTracker.isUserActive) {
          pageVisibilityManager.performSilentUpdate();
        } else {
          this.showSmartUpdateDialog();
        }
      }, 10000);
    }
  }
}
```

### 3. 更新對話框改進

#### 新的對話框選項
1. **立即更新**：馬上執行更新
2. **稍後提醒 (30分鐘)**：30分鐘後再次提醒
3. **下次進入時更新**：下次進入頁面時自動更新

#### 視覺改進
- 更友好的UI設計
- 清楚的說明文字
- 動畫效果提升體驗
- 響應式設計

### 4. 通知系統

#### 靜默更新通知
```typescript
showSilentUpdateNotification() {
  const notification = document.createElement('div');
  // 顯示"正在更新到最新版本..."的通知
  // 2秒後自動消失並刷新頁面
}
```

#### 更新進度顯示
- 顯示更新進度
- 載入動畫
- 錯誤處理

## 使用場景

### 場景1：用戶重新進入頁面
```
用戶關閉頁面 → 重新打開 → 檢測到更新 → 顯示更新通知 → 自動刷新
```

### 場景2：用戶正在使用
```
檢測到更新 → 判斷用戶活躍 → 顯示更新對話框 → 用戶選擇更新時機
```

### 場景3：用戶不活躍
```
檢測到更新 → 判斷用戶不活躍 → 等待10秒 → 執行靜默更新
```

### 場景4：Service Worker通知
```
SW發送更新通知 → 根據用戶狀態 → 選擇對話框或靜默更新
```

## 配置選項

### 時間設置
- **活動超時**：30秒無活動視為非活躍
- **延遲更新**：不活躍用戶10秒後自動更新
- **提醒間隔**：30分鐘後再次提醒
- **更新延遲**：2秒顯示更新通知

### 事件監聽
- **用戶活動事件**：mousedown, mousemove, keypress, scroll, touchstart, click
- **頁面可見性**：visibilitychange
- **頁面卸載**：beforeunload

## 技術實現

### 1. 狀態管理
使用 `sessionStorage` 追蹤：
- `app-session-started`：會話是否已開始
- `page-was-hidden`：頁面是否曾被隱藏
- `pending-update`：是否有待處理的更新

### 2. 事件通信
- **自定義事件**：`version-update-detected`
- **Service Worker消息**：`RELOAD_PAGE`, `FORCE_RELOAD`
- **控制器變更**：`controllerchange`

### 3. 公共API
在 `versionChecker` 中新增：
```typescript
public async triggerUpdate(): Promise<void>
public async manualVersionCheck(): Promise<boolean>
```

## 優化效果

### 1. 用戶體驗改善
- ✅ **避免操作中斷**：不會在用戶操作時強制刷新
- ✅ **用戶控制權**：用戶可以選擇更新時機
- ✅ **清楚的反饋**：明確告知為什麼需要更新

### 2. 智能化程度提升
- ✅ **狀態感知**：根據用戶狀態選擇更新策略
- ✅ **場景適應**：不同場景使用不同的更新方式
- ✅ **優雅降級**：網絡問題時仍能正常工作

### 3. 開發體驗
- ✅ **開發環境友好**：開發時仍保持快速更新
- ✅ **詳細日誌**：完整的更新過程記錄
- ✅ **錯誤處理**：完善的錯誤恢復機制

## 測試建議

### 1. 新會話測試
1. 關閉頁面
2. 部署新版本
3. 重新打開頁面
4. 驗證是否自動更新

### 2. 活躍用戶測試
1. 在頁面上操作
2. 觸發版本更新
3. 驗證是否顯示對話框

### 3. 不活躍用戶測試
1. 停止操作30秒以上
2. 觸發版本更新
3. 驗證是否延遲後自動更新

### 4. 頁面切換測試
1. 切換到其他標籤頁
2. 部署新版本
3. 切換回來
4. 驗證更新行為

## 注意事項

1. **兼容性**：確保在各種瀏覽器中正常工作
2. **性能**：活動追蹤不應影響頁面性能
3. **安全性**：更新過程中保護用戶數據
4. **可訪問性**：對話框支持鍵盤操作

## 總結

這個優化方案提供了：
- 🎯 **智能更新策略**：根據用戶狀態選擇最佳更新方式
- 🛡️ **用戶體驗保護**：避免操作中斷和數據丟失
- 🎛️ **用戶控制權**：讓用戶決定更新時機
- 📱 **多場景適應**：適應不同的使用場景
- 🔧 **開發友好**：保持開發環境的便利性

現在用戶不會再遇到突然的頁面刷新，而是能夠在合適的時機進行更新，大大提升了整體的用戶體驗。
