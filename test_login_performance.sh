#!/bin/bash

# 登入性能測試腳本

API_URL="http://localhost:8088/api/auth/login"
TEST_USER_UID="test_user"
TEST_USER_PWD="password"

echo "🧪 開始登入性能測試..."
echo "API URL: $API_URL"
echo "測試用戶: $TEST_USER_UID"
echo "================================"

# 測試函數
test_login() {
    local test_name="$1"
    local device_header="$2"
    
    echo "📱 測試: $test_name"
    
    start_time=$(date +%s.%N)
    
    response=$(curl -s -w "\n%{http_code}\n%{time_total}" \
        -X POST "$API_URL" \
        -H "Content-Type: application/json" \
        -H "User-Agent: $device_header" \
        -d "{\"uid\":\"$TEST_USER_UID\",\"pwd\":\"$TEST_USER_PWD\"}")
    
    end_time=$(date +%s.%N)
    
    # 解析響應
    http_code=$(echo "$response" | tail -n 2 | head -n 1)
    time_total=$(echo "$response" | tail -n 1)
    response_body=$(echo "$response" | head -n -2)
    
    duration=$(echo "$end_time - $start_time" | bc)
    
    echo "  HTTP狀態碼: $http_code"
    echo "  響應時間: ${time_total}s"
    echo "  總耗時: ${duration}s"
    
    if [ "$http_code" = "200" ]; then
        echo "  ✅ 登入成功"
        # 提取access_token（如果需要）
        access_token=$(echo "$response_body" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
        if [ -n "$access_token" ]; then
            echo "  🔑 Token已獲取"
        fi
    else
        echo "  ❌ 登入失敗"
        echo "  響應內容: $response_body"
    fi
    
    echo ""
    sleep 1
}

# 測試1: 第一次登入
test_login "第一次登入" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"

# 測試2: 第二個設備登入（應該觸發強制登出）
test_login "第二個設備登入" "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1"

# 測試3: 第一個設備再次登入
test_login "第一個設備再次登入" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"

# 測試4: 快速連續登入測試
echo "🚀 快速連續登入測試..."
for i in {1..5}; do
    echo "第 $i 次快速登入:"
    test_login "快速登入 $i" "TestDevice-$i"
done

echo "================================"
echo "✅ 測試完成"
echo ""
echo "📊 性能指標:"
echo "- 正常登入應在 1 秒內完成"
echo "- HTTP狀態碼應為 200"
echo "- 應該能獲取到 access_token"
echo ""
echo "🔍 如果發現問題:"
echo "1. 檢查後端日誌: docker logs lottery-api"
echo "2. 檢查數據庫連接"
echo "3. 檢查WebSocket連接狀態"
