package main

import (
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"github.com/robfig/cron/v3"
	log "github.com/sirupsen/logrus"

	. "lottery/controllers"
	. "lottery/models"
	. "lottery/utils"
)

func init() {
	log.SetFormatter(&log.JSONFormatter{})
	log.SetOutput(os.Stdout)

	// set time zone
	twTimeZone, err := time.LoadLocation("Asia/Taipei")
	if err != nil {
		ErrorLog(ErrorMsg{
			Msg:   "設定時區失敗",
			Error: err.Error(),
		})
		return
	}

	time.Local = twTimeZone
}

func main() {
	c := cron.New(
		cron.WithLocation(time.Local),
		cron.WithChain(
			cron.Recover(cron.DefaultLogger), // 添加恢復機制和日誌記錄
		),
	)

	// 台灣彩券定時任務：每晚 21-23 點每 10 分鐘執行一次
	_, err := c.AddFunc("*/10 21-23 * * *", func() {
		executeFetchDataWithRecovery()
	})
	if err != nil {
		log.Fatalf("添加台灣彩券定時任務失敗: %v", err)
	}

	// 加州天天樂定時任務：台灣時間早上 9:30 開始，每半小時執行一次，直到中午 12:00
	// 9:30, 10:00, 10:30, 11:00, 11:30, 12:00
	_, err = c.AddFunc("*/30 9-12 * * *", func() {
		executeCaLottoFetchWithRecovery()
	})
	if err != nil {
		log.Fatalf("添加加州天天樂 定時任務失敗: %v", err)
	}

	// 啟動 cron
	c.Start()
	defer c.Stop()

	// 立即執行一次數據抓取
	executeFetchDataWithRecovery()
	executeCaLottoFetchWithRecovery()

	// 優雅的關閉處理
	stop := make(chan os.Signal, 1)
	signal.Notify(stop, syscall.SIGINT, syscall.SIGTERM)
	<-stop

	log.Println("程序正常關閉")
}

// executeFetchDataWithRecovery 提供了更安全的數據抓取方法
func executeFetchDataWithRecovery() {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("數據抓取發生 panic: %v", r)
		}
	}()

	// 使用 WaitGroup 確保所有抓取任務完成
	var wg sync.WaitGroup
	wg.Add(4)

	go func() {
		defer wg.Done()
		CheckTWLotto(SuperLottoResult{}, SuperLotto638)
	}()

	go func() {
		defer wg.Done()
		CheckTWLotto(Lotto649Result{}, Lotto649)
	}()

	go func() {
		defer wg.Done()
		CheckTWLotto(Lotto539Result{}, Daily539)
	}()

	go func() {
		defer wg.Done()
		CheckHKLotto()
	}()

	// 等待所有任務完成，並設置超時
	waitChan := make(chan struct{})
	go func() {
		wg.Wait()
		close(waitChan)
	}()

	select {
	case <-waitChan:
		log.Println("所有數據抓取任務完成")
	case <-time.After(120 * time.Second):
		log.Println("數據抓取任務超時")
	}
}

// executeCaLottoFetchWithRecovery 專門用於加州天天樂數據抓取
func executeCaLottoFetchWithRecovery() {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("加州天天樂數據抓取發生 panic: %v", r)
		}
	}()

	log.Println("開始抓取加州天天樂數據")

	// 設置超時
	done := make(chan bool, 1)
	go func() {
		CheckCaliforniaLotto()
		done <- true
	}()

	select {
	case <-done:
		log.Println("加州天天樂數據抓取完成")
	case <-time.After(60 * time.Second):
		log.Println("加州天天樂數據抓取超時")
	}
}
