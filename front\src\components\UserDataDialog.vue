<template>
  <q-card>
    <q-card-section class="row items-center">
      <div class="text-h6">用戶資料</div>
      <q-space />
      <q-btn icon="close" flat round dense @click="close" v-if="visible" />
    </q-card-section>
    <q-card-section>
      <q-form @submit="updateUserData" class="q-py-lg q-px-sm">
        <div class="row q-col-gutter-md">
          <div class="col-12 col-sm-4">
            <q-input type="text" v-model="formData.uid" label="帳號" disable />
          </div>
          <div class="col-12 col-sm-4">
            <q-input
              type="text"
              v-model="formData.name"
              label="名稱"
              :rules="[(val) => !!val || '請輸入名稱']"
              lazy-rules
              aria-required
            />
          </div>
          <div class="col-12 col-sm-4">
            <q-select
              v-model="formData.is_active"
              label="狀態"
              :options="[
                { label: '啟用', value: true },
                { label: '停用', value: false },
              ]"
              emit-value
              map-options
            />
          </div>
          <div class="col-12 col-sm-4">
            <q-input
              type="text"
              v-model="formData.expires_at"
              label="使用期限"
              mask="date"
              :rules="[
                (val) =>
                  !val ||
                  (val &&
                    val.length == 10 &&
                    !isNaN(new Date(val).getTime())) ||
                  '請輸入正確的日期',
              ]"
              lazy-rules
            >
              <template v-slot:append>
                <q-icon name="event" class="cursor-pointer">
                  <q-popup-proxy
                    cover
                    transition-show="scale"
                    transition-hide="scale"
                  >
                    <q-date v-model="formData.expires_at" today-btn>
                      <div class="row items-center justify-end">
                        <q-btn flat label="關閉" color="dark" v-close-popup />
                      </div>
                    </q-date>
                  </q-popup-proxy>
                </q-icon>
              </template>
            </q-input>
          </div>
        </div>

        <div class="row q-col-gutter-md q-mt-md">
          <div class="col-12 col-sm-4">
            <q-input
              borderless
              type="text"
              v-model="formData.last_login_at"
              label="上次登入"
              disable
            />
          </div>
          <div class="col-12 col-sm-4">
            <q-input
              borderless
              type="text"
              v-model="formData.created_at"
              label="建立時間"
              disable
            />
          </div>
        </div>

        <div class="row items-center justify-end">
          <q-btn
            type="submit"
            label="儲存"
            color="secondary"
            :loading="isSubmit"
            class="q-mr-md q-py-sm q-px-lg"
          />
          <q-btn
            type="button"
            label="取消"
            color="dark"
            class="q-py-sm q-px-lg"
            @click="close"
          />
        </div>
      </q-form>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Notify } from 'quasar';
import { UserData } from '@/api/modules/user';
import USER_API from '@/api/modules/user';
import { handleError } from '@/utils/error-handler';

const props = defineProps<{
  visible?: boolean;
  data: UserData;
}>();

const emit = defineEmits(['update:visible', 'refreshData']);

const formData = ref<UserData>({
  id: '',
  uid: '',
  name: '',
  phone: '',
  email: '',
  is_active: false,
  expires_at: null,
  last_login_at: '',
  created_at: '',
});

onMounted(() => {
  formData.value = { ...props.data };
});

const close = () => {
  emit('update:visible', false);
};

const isSubmit = ref(false);
const updateUserData = async () => {
  try {
    isSubmit.value = true;
    await USER_API.updateUser(formData.value);
    Notify.create({
      message: '用戶資料已更新',
      color: 'positive',
      timeout: 2000,
      position: 'top',
    });
    emit('refreshData');
    close();
  } catch (error) {
    handleError(error);
  } finally {
    isSubmit.value = false;
  }
};
</script>
