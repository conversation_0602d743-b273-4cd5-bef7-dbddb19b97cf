package controllers

import (
	"encoding/json"
	"errors"
	"fmt"
	. "lottery/models"
	"net/http"

	querystring "github.com/google/go-querystring/query"
)

type TaiwanLotteryClient struct {
	LottoType LottoType                  `json:"lottoType"`
	Params    TaiwanLotteryRequestParams `json:"params"`
	Results   []LottoResult              `json:"results"`
}

func NewTaiwanLotteryClient() *TaiwanLotteryClient {
	client := &TaiwanLotteryClient{
		Params: TaiwanLotteryRequestParams{
			PageNum:  1,
			PageSize: 50,
		},
	}

	return client
}

func (client *TaiwanLotteryClient) SetPeriod(period int) *TaiwanLotteryClient {
	client.Params.Period = period
	return client
}

func (client *TaiwanLotteryClient) SetMonth(month string) *TaiwanLotteryClient {
	client.Params.Month = month
	return client
}

func (client *TaiwanLotteryClient) SetLottoType(lottoType LottoType) *TaiwanLotteryClient {
	client.LottoType = lottoType
	return client
}

func (client *TaiwanLotteryClient) GetResult() error {
	if client.LottoType == 0 {
		return errors.New("lotto type is required")
	}

	if client.Params.Period == 0 && client.Params.Month == "" {
		return errors.New("period or month is required")
	}

	_, err := client.request()
	if err != nil {
		return err
	}

	return nil
}

func (client *TaiwanLotteryClient) request() (*http.Response, error) {
	requestUrl := TaiwanLotteryRequestUrl[client.LottoType]
	q, err := querystring.Values(client.Params)
	if err != nil {
		return nil, err
	}

	if len(q) > 0 {
		requestUrl = requestUrl + "?" + q.Encode()
	}

	req, err := http.NewRequest(http.MethodGet, requestUrl, nil)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")

	c := &http.Client{}
	res, err := c.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()

	if res.StatusCode > http.StatusIMUsed {
		return nil, fmt.Errorf("request failed: %s", res.Status)
	}

	response := createLotteryResponse(client.LottoType)
	if err := json.NewDecoder(res.Body).Decode(response); err != nil {
		return res, err
	}

	taiwanLotteryRes := response.(LottoResponse).GetLottoResults()
	results := []LottoResult{}
	for _, r := range taiwanLotteryRes {
		specialNumber := 0
		if len(r.DrawNumberSize) > 5 {
			lastIndex := len(r.DrawNumberSize) - 1
			specialNumber = r.DrawNumberSize[lastIndex]
			r.DrawNumberSize = r.DrawNumberSize[:lastIndex]
			r.DrawNumberAppear = r.DrawNumberAppear[:lastIndex]
		}

		results = append(results, LottoResult{
			Period:           r.Period,
			DrawDate:         r.LotteryDate,
			DrawNumberSize:   r.DrawNumberSize,
			DrawNumberAppear: r.DrawNumberAppear,
			SpecialNumber:    specialNumber,
		})
	}
	client.Results = results

	return res, nil
}

func createLotteryResponse(lottoType LottoType) interface{} {
	switch lottoType {
	case SuperLotto638:
		return &SuperLottoResponse{}
	case Lotto649:
		return &Lotto649Response{}
	case Daily539:
		return &Daily539Response{}
	default:
		return nil
	}
}
