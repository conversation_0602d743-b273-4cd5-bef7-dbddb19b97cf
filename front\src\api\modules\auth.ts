import axios from '../axios';
import { api } from '@/boot/axios';

// 定義介面
interface RegisterData {
  uid: string;
  pwd: string;
  name: string;
  phone: string;
  email: string;
}

interface LoginPayload {
  uid: string;
  pwd: string;
  device_id?: string;
  device_fingerprint?: {
    stable_device_id: string;
    session_id: string;
    confidence: number;
    platform: string;
    screen: string;
  };
}

export interface User {
  id: number;
  uid: string;
  name: string;
}

export interface UserProfile {
  id: number;
  uid: string;
  name: string;
  email: string;
  is_active: boolean;
  expires_at: string | null;
  last_login_at: string | null;
  created_at: string;
}

export interface UpdateProfileData {
  name: string;
  email: string;
}

export interface ChangePasswordData {
  new_password: string;
}

export interface TokenResponse {
  access_token: string;
  expires_at: Date;
  refresh_token: string;
  refresh_expires_at: Date;
}

export interface LoginResponse extends TokenResponse {
  user: User;
}

const AUTH_API = {
  register: (data: RegisterData) => axios.post('/auth/register', data),
  login: (loginPayload: LoginPayload) =>
    axios.post<LoginResponse>('/auth/login', loginPayload),
  logout: () => api.post('/auth/logout'),
  getCurrentUser: () => api.get<User>('/auth/user'),
  refreshToken: () => api.post<TokenResponse>('/auth/refresh-token'),
  // 個人資料相關 API
  getUserProfile: () => api.get<UserProfile>('/profile'),
  updateUserProfile: (data: UpdateProfileData) => api.patch('/profile', data),
  changePassword: (data: ChangePasswordData) => api.patch('/profile/password', data),
};

export default AUTH_API;
