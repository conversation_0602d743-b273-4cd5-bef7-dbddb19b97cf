package models

type Pagination struct {
	Props  PageProps `form:"props" json:"props"`
	Filter string    `form:"filter"`
}

type PageProps struct {
	Page        int    `form:"page" json:"page"`
	RowsPerPage int    `form:"rowsPerPage" json:"rowsPerPage"`
	SortBy      string `form:"sortBy" json:"sortBy"`
	Descending  bool   `form:"descending" json:"descending"`
	RowsNumber  int64  `form:"rowsNumber" json:"rowsNumber"`
}

func (p *PageProps) GetOffset() int {
	return (p.Page - 1) * p.RowsPerPage
}

func (p *PageProps) GetLimit() int {
	return p.RowsPerPage
}

func (p *PageProps) GetOrderBy() string {
	if p.Descending {
		return p.SortBy + " DESC"
	}
	return p.SortBy
}
