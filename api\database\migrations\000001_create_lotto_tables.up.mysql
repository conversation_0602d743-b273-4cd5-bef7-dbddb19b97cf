CREATE TABLE IF NOT EXISTS `super_lotto_results` (
    `period`  INT NOT NULL PRIMARY KEY,
    `draw_date` DATE NOT NULL UNIQUE,
    `draw_number_size` JSON NOT NULL,
    `draw_number_appear` JSON NOT NULL,
    `special_number` TINYINT UNSIGNED NOT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `lotto649_results` (
    `period`  INT NOT NULL PRIMARY KEY,
    `draw_date` DATE NOT NULL UNIQUE,
    `draw_number_size` JSON NOT NULL,
    `draw_number_appear` JSON NOT NULL,
    `special_number` TINYINT UNSIGNED NOT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `lotto539_results` (
    `period`  INT NOT NULL PRIMARY KEY,
    `draw_date` DATE NOT NULL UNIQUE,
    `draw_number_size` JSON NOT NULL,
    `draw_number_appear` JSON NOT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `lotto_hk_results` (
    `period`  INT NOT NULL PRIMARY KEY,
    `draw_date` DATE NOT NULL UNIQUE,
    `draw_number_size` JSON NOT NULL,
    `draw_number_appear` JSON NOT NULL,
    `special_number` TINYINT UNSIGNED NOT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;