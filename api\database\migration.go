package database

import (
	"database/sql"

	log "github.com/sirupsen/logrus"

	_ "github.com/go-sql-driver/mysql"
	"github.com/golang-migrate/migrate/v4"
	"github.com/golang-migrate/migrate/v4/database/mysql"
	_ "github.com/golang-migrate/migrate/v4/source/file"
	"github.com/spf13/viper"
)

const MigrationPath = "file://database/migrations"

func Migration() {
	// 连接到数据库
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Fatalf("Could not connect to database: %v", err)
		return
	}
	defer db.Close()

	// 创建 mysql driver
	driver, err := mysql.WithInstance(db, &mysql.Config{})
	if err != nil {
		log.Fatalf("Could not create mysql driver: %v", err)
		return
	}

	// 初始化 migrate
	m, err := migrate.NewWithDatabaseInstance(
		MigrationPath,
		viper.Get("DB_NAME").(string),
		driver,
	)
	if err != nil {
		log.Fatalf("Migration initialization failed: %v", err)
		return
	}

	// 执行迁移
	err = m.Up()
	if err != nil {
		if err == migrate.ErrNoChange {
			return
		} else {
			log.Fatalf("Migration failed: %v", err)
			// 这里处理迁移失败
			handleMigrationFailure(m, err)
		}
	}
}

func handleMigrationFailure(m *migrate.Migrate, err error) {
	// 这里处理迁移失败
	// 例如回滚迁移
	// m.Down()
}
