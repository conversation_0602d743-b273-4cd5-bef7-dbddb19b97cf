package controllers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"lottery/models"
	. "lottery/utils"
)

type CaLotteryClient struct {
	Params  models.CaLottoRequestParams    `json:"params"`
	Results []models.LottoCaliforniaResult `json:"results"`
}

func NewCaLotteryClient() *CaLotteryClient {
	client := &CaLotteryClient{
		Params: models.CaLottoRequestParams{
			Year: 2022,
		},
	}

	return client
}

func (client *CaLotteryClient) SetYear(year int) *CaLotteryClient {
	client.Params.Year = year
	return client
}

func (client *CaLotteryClient) GetResult() error {
	err := client.request()

	return err
}

// CaLotteryAPIResponse 定義 API 響應結構
type CaLotteryAPIResponse struct {
	PageProps struct {
		LotteryCA5List []CaLotteryItem `json:"lotteryCA5List"`
	} `json:"pageProps"`
}

// CaLotteryItem 定義單個開獎結果結構
type CaLotteryItem struct {
	Period         int      `json:"period"`
	DrawDate       string   `json:"drawDate"`
	JackpotAmount  int      `json:"jackpotAmount"`
	WinningNumbers []string `json:"winningNumbers"`
	BetNumber1     int      `json:"betNumber1"`
	BetPrice1      int      `json:"betPrice1"`
}

func (client *CaLotteryClient) request() error {
	// 構建請求 URL
	requestUrl := fmt.Sprintf("%s/%d.json", models.CaLotteryUrl, client.Params.Year)

	// 創建 HTTP 請求
	req, err := http.NewRequest(http.MethodGet, requestUrl, nil)
	if err != nil {
		return fmt.Errorf("創建請求失敗: %w", err)
	}

	// 設置請求頭
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

	// 發送請求
	httpClient := &http.Client{
		Timeout: 30 * time.Second,
	}
	res, err := httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("請求失敗: %w", err)
	}
	defer res.Body.Close()

	// 檢查響應狀態
	if res.StatusCode != http.StatusOK {
		return fmt.Errorf("請求失敗，狀態碼: %d", res.StatusCode)
	}

	// 解析 JSON 響應
	var apiResponse CaLotteryAPIResponse
	if err := json.NewDecoder(res.Body).Decode(&apiResponse); err != nil {
		return fmt.Errorf("解析 JSON 失敗: %w", err)
	}

	// 轉換為 LottoCaliforniaResult
	var results []models.LottoCaliforniaResult
	for _, item := range apiResponse.PageProps.LotteryCA5List {
		// 解析開獎日期
		drawDate, err := time.Parse("2006-01-02T15:04:05.000Z", item.DrawDate)
		if err != nil {
			ErrorLog(ErrorMsg{
				Msg:   fmt.Sprintf("解析日期失敗，期數: %d", item.Period),
				Error: err.Error(),
			})
			continue
		}

		// 轉換中獎號碼為整數陣列
		var winningNumbers []int
		for _, numStr := range item.WinningNumbers {
			// 移除前導零並轉換為整數
			numStr = strings.TrimLeft(numStr, "0")
			if numStr == "" {
				numStr = "0"
			}
			num, err := strconv.Atoi(numStr)
			if err != nil {
				ErrorLog(ErrorMsg{
					Msg:   fmt.Sprintf("轉換號碼失敗，期數: %d, 號碼: %s", item.Period, numStr),
					Error: err.Error(),
				})
				continue
			}
			winningNumbers = append(winningNumbers, num)
		}

		// 創建 LottoCaliforniaResult
		result := models.LottoCaliforniaResult{
			LottoResult: models.LottoResult{
				Period:           item.Period,
				DrawDate:         drawDate.Format("2006-01-02"),
				DrawNumberSize:   winningNumbers, // 由於 winningNumbers 已經是按大小排序，直接使用
				DrawNumberAppear: winningNumbers, // 同樣使用 winningNumbers
				SpecialNumber:    0,              // 加州天天樂沒有特別號
			},
		}

		results = append(results, result)
	}

	client.Results = results
	return nil
}
