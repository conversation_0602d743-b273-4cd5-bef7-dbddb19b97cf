# 日期格式化工具函數

這個模組提供了一套完整的日期格式化工具函數，專為台灣地區的日期時間顯示需求設計。

## 主要功能

### 基本格式化函數

#### `formatDate(date, options)`
最通用的日期格式化函數，支援多種自定義選項。

```typescript
import { formatDate } from '@/utils';

// 基本使用
formatDate(new Date()); // "2024/01/15"

// 自定義選項
formatDate(new Date(), {
  showTime: true,
  hour12: true,
  showSeconds: true,
  dateSeparator: '-',
  timeSeparator: ':'
}); // "2024-01-15 下午 02:30:45"
```

#### `formatDateTW(date)` 和 `formatDateTimeTW(date)`
專為台灣地區設計的標準格式化函數。

```typescript
import { formatDateTW, formatDateTimeTW } from '@/utils';

formatDateTW('2024-01-15'); // "2024/01/15"
formatDateTimeTW('2024-01-15T14:30:00'); // "2024/01/15 14:30"
```

### 智能格式化

#### `formatSmartDate(date)`
根據日期自動選擇最合適的顯示格式。

```typescript
import { formatSmartDate } from '@/utils';

formatSmartDate(new Date()); // "今天 14:30"
formatSmartDate(yesterday); // "昨天 09:15"
formatSmartDate(lastWeek); // "2024/01/08"
```

#### `formatRelativeTime(date)`
顯示相對時間（如：2小時前、3天前）。

```typescript
import { formatRelativeTime } from '@/utils';

formatRelativeTime(new Date(Date.now() - 2 * 60 * 60 * 1000)); // "2小時前"
formatRelativeTime(new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)); // "3天前"
```

### 專用格式化

#### `formatDateOnly(date, separator)`
僅顯示日期部分。

```typescript
import { formatDateOnly } from '@/utils';

formatDateOnly('2024-01-15T14:30:00'); // "2024/01/15"
formatDateOnly('2024-01-15T14:30:00', '-'); // "2024-01-15"
```

#### `formatTimeOnly(date, showSeconds, hour12)`
僅顯示時間部分。

```typescript
import { formatTimeOnly } from '@/utils';

formatTimeOnly('2024-01-15T14:30:45'); // "14:30"
formatTimeOnly('2024-01-15T14:30:45', true); // "14:30:45"
formatTimeOnly('2024-01-15T14:30:45', false, true); // "下午 2:30"
```

#### `formatDateISO(date)`
格式化為 ISO 日期格式 (YYYY-MM-DD)。

```typescript
import { formatDateISO } from '@/utils';

formatDateISO(new Date()); // "2024-01-15"
```

### 日期範圍和計算

#### `formatDateRange(startDate, endDate, options)`
格式化日期範圍。

```typescript
import { formatDateRange } from '@/utils';

formatDateRange('2024-01-01', '2024-01-15'); // "2024/01/01 至 2024/01/15"
formatDateRange('2024-01-01', null); // "2024/01/01 至今"
```

#### `getDaysDifference(startDate, endDate)`
計算兩個日期之間的天數差。

```typescript
import { getDaysDifference } from '@/utils';

getDaysDifference('2024-01-01', '2024-01-15'); // 14
```

### 日期檢查函數

#### `isValidDate(date)`
檢查日期是否有效。

```typescript
import { isValidDate } from '@/utils';

isValidDate('2024-01-15'); // true
isValidDate('invalid-date'); // false
```

#### `isToday(date)` 和 `isYesterday(date)`
檢查日期是否為今天或昨天。

```typescript
import { isToday, isYesterday } from '@/utils';

isToday(new Date()); // true
isYesterday(new Date(Date.now() - 24 * 60 * 60 * 1000)); // true
```

## 在 Vue 組件中使用

### 在模板中使用

```vue
<template>
  <div>
    <p>建立時間: {{ formatDateTimeTW(user.created_at) }}</p>
    <p>最後登入: {{ formatSmartDate(user.last_login_at) || '尚未登入' }}</p>
    <p>使用期限: {{ formatDateTW(user.expires_at) || '無期限' }}</p>
  </div>
</template>

<script setup>
import { formatDateTW, formatDateTimeTW, formatSmartDate } from '@/utils';
</script>
```

### 在表格中使用

```typescript
const columns = [
  {
    name: 'created_at',
    label: '建立時間',
    field: 'created_at',
    format: (val: string) => formatDateTimeTW(val),
    align: 'center' as const,
    sortable: true,
  },
  {
    name: 'expires_at',
    label: '使用期限',
    field: 'expires_at',
    format: (val: string | null) => val ? formatDateTW(val) : '無期限',
    align: 'center' as const,
    sortable: true,
  }
];
```

## 配置選項

### DateFormatOptions 介面

```typescript
interface DateFormatOptions {
  showTime?: boolean;        // 是否顯示時間
  hour12?: boolean;          // 是否使用 12 小時制
  showSeconds?: boolean;     // 是否顯示秒數
  dateSeparator?: string;    // 日期分隔符
  timeSeparator?: string;    // 時間分隔符
  customFormat?: 'short' | 'medium' | 'long' | 'full';
  locale?: string;           // 語言地區
}
```

## 錯誤處理

所有函數都包含錯誤處理機制：
- 無效日期會返回空字串
- 錯誤會記錄到控制台
- 不會拋出異常，確保應用程式穩定性

## 性能考量

- 函數經過優化，適合在大量數據的表格中使用
- 支援 null/undefined 值的安全處理
- 使用原生 JavaScript Date API，性能良好
