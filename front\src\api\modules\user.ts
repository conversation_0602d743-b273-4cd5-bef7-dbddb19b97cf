import { api } from '@/boot/axios';
import { PageProps } from '@/models/page';

export interface UserData {
  [key: string]: string | boolean | null | undefined;
  id: string;
  uid: string;
  name: string;
  phone: string;
  email: string;
  is_active: boolean;
  expires_at: string | null;
  last_login_at: string;
  created_at: string;
}

// 設備相關類型定義
export interface UserDevice {
  id: number;
  user_id: number;
  device_id: string;
  device_name: string;
  user_agent: string;
  is_active: boolean;
  last_seen_at: string;
  created_at: string;
  updated_at: string;
}

export interface LoginHistory {
  id: number;
  user_id: number;
  ip_address: string;
  user_agent: string;
  is_success: boolean;
  created_at: string;
}

const USER_API = {
  getUserList: (props?: PageProps, filter?: string) =>
    api.get<{ data: UserData[]; pageProps: PageProps }>('/admin/users', {
      params: { ...props, filter },
    }),
  updateUser: (data: UserData) => {
    try {
      if (data.expires_at === '') {
        data.expires_at = null;
      }

      return api.patch('/admin/users', data);
    } catch (error) {
      throw error;
    }
  },
  updateUserActive: (id: string, isActive: boolean) =>
    api.patch(`/admin/users/${id}/active`, { is_active: isActive }),
  updateUserExpire: (id: string, expiresAt: string | null) => {
    try {
      if (expiresAt === '') {
        expiresAt = null;
      }
      return api.patch(`/admin/users/${id}/expire`, { expires_at: expiresAt });
    } catch (error) {
      throw error;
    }
  },
  deleteUser: (id: string) => api.delete(`/admin/users/${id}`),
  // 設備管理相關API
  getUserDevices: (userId: string) =>
    api.get<{ devices: UserDevice[] }>(`/admin/users/${userId}/devices`),
  getUserLoginHistory: (userId: string) =>
    api.get<{ login_history: LoginHistory[] }>(`/admin/users/${userId}/login-history`),
  deleteUserDevice: (userId: string, deviceId: string) =>
    api.delete(`/admin/users/${userId}/devices/${deviceId}`),
};

export default USER_API;
