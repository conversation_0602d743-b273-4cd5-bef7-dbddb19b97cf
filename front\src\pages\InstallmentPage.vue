<template>
  <q-page>
    <q-card>
      <q-card-section>
        <q-btn
          v-if="deferredPrompt"
          label="安裝到桌面"
          color="primary"
          @click="installPWA"
        />
        <q-btn
          v-else-if="isInstallable && !isStandalone && !isIOS"
          label="檢查安裝狀態"
          color="secondary"
          @click="checkInstallability"
        />
        <div v-if="installMessage" class="q-mt-sm text-h6">
          {{ installMessage }}
        </div>
      </q-card-section>

      <q-card-section v-if="isIOS">
        <div class="row q-mb-md">
          <div class="col col-12 text-h6 q-mb-sm">
            1. 開啟 Safari
            <q-img
              src="images/safari.png"
              width="35px"
              height="35px"
              fit="contain"
            />
            ，點擊下方按鈕。
          </div>
          <div class="col col-12">
            <q-img src="images/install-1.jpg" width="200px" fit="contain" />
          </div>
        </div>
        <div class="row q-mb-md">
          <div class="col col-12 text-h6 q-mb-sm">
            2. 在功能表中，點擊【加入主畫面】。
          </div>
          <div class="col col-12">
            <q-img src="images/install-2.jpg" width="200px" fit="contain" />
          </div>
        </div>
        <div class="row q-mb-md">
          <div class="col col-12 text-h6 q-mb-sm">
            3. 加入成功後，網頁App就會顯示在桌面。
          </div>
          <div class="col col-12">
            <q-img
              src="images/install-3.jpg"
              width="100px"
              height="100px"
              fit="contain"
            />
          </div>
        </div>
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { SafariNavigator, pwaInstallService } from '@/services/pwaInstall';

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

const deferredPrompt = ref<BeforeInstallPromptEvent | null>(null);
const installMessage = ref('');
const isInstallable = ref(false);
const isStandalone = ref(false);
const isIOS = ref(false);

// 檢查是否可以安裝 PWA
const checkInstallability = () => {
  const result = pwaInstallService.checkInstallability();
  isInstallable.value = result.canInstall;
  installMessage.value = result.message;
  isStandalone.value = result.isStandalone;
  isIOS.value = result.isIOS;

  if (window.savedBeforeInstallPromptEvent) {
    deferredPrompt.value = window.savedBeforeInstallPromptEvent;
    isInstallable.value = true;
    installMessage.value = '您可以安裝此應用到您的裝置上';
  }

  return result.canInstall;
};

// 當頁面元件收到 beforeinstallprompt 事件時的處理
const handleBeforeInstallPrompt = (e: Event) => {
  console.log('頁面元件 beforeinstallprompt 事件已觸發');
  const promptEvent = e as BeforeInstallPromptEvent;
  e.preventDefault();

  deferredPrompt.value = promptEvent;
  window.savedBeforeInstallPromptEvent = promptEvent;
  isInstallable.value = true;
  installMessage.value = '您可以安裝此應用到您的裝置上';
};

const installPWA = async () => {
  const promptEvent =
    deferredPrompt.value || window.savedBeforeInstallPromptEvent;

  if (!promptEvent) {
    console.log('安裝提示不可用');
    checkInstallability();
    return;
  }

  try {
    await promptEvent.prompt();
    const choiceResult = await promptEvent.userChoice;

    if (choiceResult.outcome === 'accepted') {
      console.log('使用者接受了 PWA 安裝');
      installMessage.value = '應用已成功安裝';
      isStandalone.value = true;
    } else {
      console.log('使用者拒絕了 PWA 安裝');
      installMessage.value = '您取消了安裝程序';
    }
  } catch (error) {
    console.error('安裝 PWA 時發生錯誤:', error);
    installMessage.value = `安裝時發生錯誤: ${error}`;
  } finally {
    deferredPrompt.value = null;
    window.savedBeforeInstallPromptEvent = undefined;
    isInstallable.value = false;
  }
};

onMounted(() => {
  console.log('安裝頁面已掛載');

  // 確保 PWA 安裝服務已初始化
  pwaInstallService.initialize();

  // 檢查是否為獨立運行模式
  const standalone =
    window.matchMedia('(display-mode: standalone)').matches ||
    (window.navigator as SafariNavigator).standalone ||
    document.referrer.includes('android-app://');

  isStandalone.value = standalone;

  // 訂閱安裝提示事件
  const unsubscribe = pwaInstallService.onBeforeInstallPrompt((promptEvent) => {
    console.log('收到安裝提示事件');
    deferredPrompt.value = promptEvent;
    isInstallable.value = true;
    installMessage.value = '您可以安裝此應用到您的裝置上';
  });

  // 執行初始檢查
  checkInstallability();

  // 監聽安裝完成事件
  window.addEventListener('appinstalled', () => {
    console.log('PWA 已成功安裝');
    installMessage.value = '應用已成功安裝到您的裝置';
    deferredPrompt.value = null;
    window.savedBeforeInstallPromptEvent = undefined;
    isInstallable.value = false;
    isStandalone.value = true;
  });

  // 組件卸載時取消訂閱
  onUnmounted(() => {
    unsubscribe();
  });
});

onUnmounted(() => {
  window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
});
</script>
