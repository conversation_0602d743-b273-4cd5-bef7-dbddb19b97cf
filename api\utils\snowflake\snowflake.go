package snowflake

import (
	"errors"
	"log"
	"os"
	"strconv"
	"sync"
	"time"
)

const (
	// 位段劃分
	timestampBits uint8 = 41 // 時間戳佔位
	workerBits    uint8 = 10 // 機器ID佔位
	sequenceBits  uint8 = 12 // 序列號佔位

	// 最大值
	workerMax   uint64 = 1<<workerBits - 1
	sequenceMax uint64 = 1<<sequenceBits - 1

	// 位移
	workerShift    uint8 = sequenceBits
	timestampShift uint8 = workerBits + sequenceBits

	// 起始時間戳 (2020-01-01)
	epoch uint64 = 1577836800000
)

type Snowflake struct {
	mu            sync.Mutex
	lastTimestamp uint64
	workerID      uint64
	sequence      uint64
}

func NewSnowflake() (*Snowflake, error) {
	workerID := generateWorkerID()
	if workerID > workerMax {
		return nil, errors.New("worker ID exceeds maximum range")
	}
	return &Snowflake{
		workerID: workerID,
	}, nil
}

func (s *Snowflake) Generate() uint64 {
	s.mu.Lock()
	defer s.mu.Unlock()

	now := uint64(time.Now().UnixNano() / 1e6)

	if now < s.lastTimestamp {
		log.Printf("Clock moved backwards. Refusing to generate id")
		return 0
	}

	if now == s.lastTimestamp {
		s.sequence = (s.sequence + 1) & sequenceMax
		if s.sequence == 0 {
			// 序列號溢出，等待下一毫秒
			for now <= s.lastTimestamp {
				now = uint64(time.Now().UnixNano() / 1e6)
			}
		}
	} else {
		s.sequence = 0
	}

	s.lastTimestamp = now

	return ((now - epoch) << timestampShift) |
		(s.workerID << workerShift) |
		s.sequence
}

func generateWorkerID() uint64 {
	// 1. 首先嘗試從環境變量獲取
	envID := os.Getenv("WORKER_ID")
	if envID != "" {
		id, err := strconv.ParseUint(envID, 10, 64)
		if err == nil && id <= workerMax {
			return id
		}
	}

	// 2. 從主機名稱生成
	hostname, err := os.Hostname()
	if err != nil {
		log.Println("Cannot get hostname:", err)
		return 0 // 預設為0
	}

	// 使用 FNV-1a 哈希函數
	hash := fnv32a(hostname)
	return uint64(hash) % (workerMax + 1)
}

// FNV-1a 哈希函數
func fnv32a(s string) uint32 {
	hash := uint32(2166136261)
	for i := 0; i < len(s); i++ {
		hash ^= uint32(s[i])
		hash *= 16777619
	}
	return hash
}
