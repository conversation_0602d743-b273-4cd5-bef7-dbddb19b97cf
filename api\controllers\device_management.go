package controllers

import (
	"net/http"
	"strconv"
	"strings"

	. "lottery/database"
	. "lottery/models"
	"lottery/utils"

	"github.com/gin-gonic/gin"
)

// GetDeviceStatistics 獲取設備統計信息
func GetDeviceStatistics(c *gin.Context) {
	db := ConnectDB()
	defer CloseDB(db)

	cleanupService := utils.NewDeviceCleanupService(db)

	stats, err := cleanupService.GetDeviceStatistics()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "獲取設備統計信息失敗",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "獲取設備統計信息成功",
		"data":    stats,
	})
}

// CleanupDevices 清理重複設備
func CleanupDevices(c *gin.Context) {
	db := ConnectDB()
	defer CloseDB(db)

	cleanupService := utils.NewDeviceCleanupService(db)

	err := cleanupService.CleanupDuplicateDevices()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "清理設備失敗",
			"details": err.Error(),
		})
		return
	}

	// 獲取清理後的統計信息
	stats, err := cleanupService.GetDeviceStatistics()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"message": "設備清理完成，但獲取統計信息失敗",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "設備清理完成",
		"data":    stats,
	})
}

// GetUserDevicesDetailed 獲取用戶的設備列表（詳細版本）
func GetUserDevicesDetailed(c *gin.Context) {
	db := ConnectDB()
	defer CloseDB(db)

	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "無效的用戶ID",
		})
		return
	}

	var devices []UserDevice
	err = db.Where("user_id = ?", userID).
		Order("last_seen_at DESC").
		Find(&devices).Error

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "獲取用戶設備列表失敗",
			"details": err.Error(),
		})
		return
	}

	// 處理設備信息，添加解析後的用戶代理信息
	type DeviceWithParsedUA struct {
		UserDevice
		ParsedUserAgent map[string]interface{} `json:"parsed_user_agent"`
	}

	var result []DeviceWithParsedUA
	for _, device := range devices {
		parsedUA := parseUserAgentInfo(device.UserAgent)
		result = append(result, DeviceWithParsedUA{
			UserDevice:      device,
			ParsedUserAgent: parsedUA,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "獲取用戶設備列表成功",
		"data":    result,
	})
}

// GetDuplicateDevices 獲取重複設備列表
func GetDuplicateDevices(c *gin.Context) {
	type DuplicateDeviceGroup struct {
		UserID         uint64       `json:"user_id"`
		StableDeviceID string       `json:"stable_device_id"`
		Count          int          `json:"count"`
		Devices        []UserDevice `json:"devices"`
		User           User         `json:"user"`
	}

	// 查找有重複設備的用戶組
	type DeviceGroup struct {
		UserID         uint64 `json:"user_id"`
		StableDeviceID string `json:"stable_device_id"`
		Count          int    `json:"count"`
	}

	var groups []DeviceGroup

	db := ConnectDB()
	defer CloseDB(db)

	err := db.Model(&UserDevice{}).
		Select("user_id, stable_device_id, COUNT(*) as count").
		Where("stable_device_id != '' AND stable_device_id IS NOT NULL").
		Group("user_id, stable_device_id").
		Having("COUNT(*) > 1").
		Find(&groups).Error

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "獲取重複設備列表失敗",
			"details": err.Error(),
		})
		return
	}

	var result []DuplicateDeviceGroup
	for _, group := range groups {
		var devices []UserDevice
		err := db.Where("user_id = ? AND stable_device_id = ?", group.UserID, group.StableDeviceID).
			Order("last_seen_at DESC").
			Find(&devices).Error
		if err != nil {
			continue
		}

		var user User
		err = db.Where("id = ?", group.UserID).First(&user).Error
		if err != nil {
			continue
		}

		result = append(result, DuplicateDeviceGroup{
			UserID:         group.UserID,
			StableDeviceID: group.StableDeviceID,
			Count:          group.Count,
			Devices:        devices,
			User:           user,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "獲取重複設備列表成功",
		"data":    result,
	})
}

// DeleteDevice 刪除設備
func DeleteDevice(c *gin.Context) {
	deviceIDStr := c.Param("device_id")
	deviceID, err := strconv.ParseInt(deviceIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "無效的設備ID",
		})
		return
	}

	db := ConnectDB()
	defer CloseDB(db)

	// 檢查設備是否存在
	var device UserDevice
	err = db.Where("id = ?", deviceID).First(&device).Error
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "設備不存在",
		})
		return
	}

	// 刪除設備
	err = db.Delete(&device).Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "刪除設備失敗",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "設備刪除成功",
	})
}

// MergeDevices 手動合併設備
func MergeDevices(c *gin.Context) {
	var request struct {
		UserID          uint64  `json:"user_id" binding:"required"`
		StableDeviceID  string  `json:"stable_device_id" binding:"required"`
		KeepDeviceID    int64   `json:"keep_device_id" binding:"required"`
		DeleteDeviceIDs []int64 `json:"delete_device_ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "請求參數錯誤",
			"details": err.Error(),
		})
		return
	}

	db := ConnectDB()
	defer CloseDB(db)

	// 檢查要保留的設備是否存在
	var keepDevice UserDevice
	err := db.Where("id = ? AND user_id = ? AND stable_device_id = ?",
		request.KeepDeviceID, request.UserID, request.StableDeviceID).First(&keepDevice).Error
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "要保留的設備不存在或不匹配",
		})
		return
	}

	// 開始事務
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 刪除指定的設備
	for _, deviceID := range request.DeleteDeviceIDs {
		var device UserDevice
		err := tx.Where("id = ? AND user_id = ? AND stable_device_id = ?",
			deviceID, request.UserID, request.StableDeviceID).First(&device).Error
		if err != nil {
			tx.Rollback()
			c.JSON(http.StatusBadRequest, gin.H{
				"error":     "要刪除的設備不存在或不匹配",
				"device_id": deviceID,
			})
			return
		}

		err = tx.Delete(&device).Error
		if err != nil {
			tx.Rollback()
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":     "刪除設備失敗",
				"device_id": deviceID,
			})
			return
		}
	}

	// 提交事務
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "合併設備失敗",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":     "設備合併成功",
		"kept_device": keepDevice,
	})
}

// parseUserAgentInfo 解析用戶代理信息
func parseUserAgentInfo(userAgent string) map[string]interface{} {
	// 這裡可以使用更複雜的用戶代理解析邏輯
	// 為了簡化，這裡只提取基本信息
	result := make(map[string]interface{})

	ua := strings.ToLower(userAgent)

	// 檢測瀏覽器
	if strings.Contains(ua, "chrome") {
		result["browser"] = "Chrome"
	} else if strings.Contains(ua, "firefox") {
		result["browser"] = "Firefox"
	} else if strings.Contains(ua, "safari") && !strings.Contains(ua, "chrome") {
		result["browser"] = "Safari"
	} else if strings.Contains(ua, "edge") {
		result["browser"] = "Edge"
	} else {
		result["browser"] = "Unknown"
	}

	// 檢測操作系統
	if strings.Contains(ua, "windows") {
		result["os"] = "Windows"
	} else if strings.Contains(ua, "mac os x") {
		result["os"] = "macOS"
	} else if strings.Contains(ua, "android") {
		result["os"] = "Android"
	} else if strings.Contains(ua, "iphone") || strings.Contains(ua, "ipad") {
		result["os"] = "iOS"
	} else if strings.Contains(ua, "linux") {
		result["os"] = "Linux"
	} else {
		result["os"] = "Unknown"
	}

	// 檢測設備類型
	if strings.Contains(ua, "mobile") || strings.Contains(ua, "android") && !strings.Contains(ua, "tablet") {
		result["device_type"] = "Mobile"
	} else if strings.Contains(ua, "tablet") || strings.Contains(ua, "ipad") {
		result["device_type"] = "Tablet"
	} else {
		result["device_type"] = "Desktop"
	}

	return result
}
