package utils

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"regexp"
	"strings"
	"time"

	"gorm.io/gorm"
)

// DeviceRecord 本地設備記錄結構體（避免循環導入）
type DeviceRecord struct {
	ID             int64           `gorm:"primaryKey" json:"id"`
	UserID         uint64          `json:"user_id"`
	DeviceID       string          `json:"device_id"`
	StableDeviceID string          `json:"stable_device_id"`
	DeviceName     string          `json:"device_name"`
	UserAgent      string          `json:"user_agent"`
	IsActive       bool            `json:"is_active"`
	LastSeenAt     time.Time       `json:"last_seen_at"`
	Confidence     int             `json:"confidence"`
	CreatedAt      time.Time       `json:"created_at"`
	UpdatedAt      time.Time       `json:"updated_at"`
	DeletedAt      *gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName 指定表名
func (DeviceRecord) TableName() string {
	return "user_devices"
}

// DeviceCleanupService 設備清理服務
type DeviceCleanupService struct {
	db *gorm.DB
}

// NewDeviceCleanupService 創建設備清理服務
func NewDeviceCleanupService(db *gorm.DB) *DeviceCleanupService {
	return &DeviceCleanupService{db: db}
}

// CleanupDuplicateDevices 清理重複的設備記錄
func (s *DeviceCleanupService) CleanupDuplicateDevices() error {
	// 1. 為現有記錄生成穩定設備ID（如果沒有的話）
	err := s.generateMissingStableDeviceIDs()
	if err != nil {
		return err
	}

	// 2. 合併同一物理設備的記錄
	err = s.mergeSamePhysicalDevices()
	if err != nil {
		return err
	}

	// 3. 清理過期的設備記錄
	err = s.cleanupInactiveDevices()
	if err != nil {
		return err
	}

	return nil
}

// generateMissingStableDeviceIDs 為缺少穩定設備ID的記錄生成ID
func (s *DeviceCleanupService) generateMissingStableDeviceIDs() error {
	var devices []DeviceRecord
	err := s.db.Where("stable_device_id = '' OR stable_device_id IS NULL").Find(&devices).Error
	if err != nil {
		return err
	}

	for _, device := range devices {
		stableDeviceID := generateStableDeviceIDFromUserAgent(device.UserAgent)

		err := s.db.Model(&device).Update("stable_device_id", stableDeviceID).Error
		if err != nil {
			ErrorLog(ErrorMsg{
				Error: err.Error(),
				Msg:   "更新設備穩定設備ID失敗",
			})
			continue
		}
	}

	return nil
}

// mergeSamePhysicalDevices 合併同一物理設備的記錄
func (s *DeviceCleanupService) mergeSamePhysicalDevices() error {
	// 查找有相同穩定設備ID的用戶設備組
	type DeviceGroup struct {
		UserID         uint64 `json:"user_id"`
		StableDeviceID string `json:"stable_device_id"`
		Count          int    `json:"count"`
	}

	var groups []DeviceGroup
	err := s.db.Model(&DeviceRecord{}).
		Select("user_id, stable_device_id, COUNT(*) as count").
		Where("stable_device_id != '' AND stable_device_id IS NOT NULL").
		Group("user_id, stable_device_id").
		Having("COUNT(*) > 1").
		Find(&groups).Error

	if err != nil {
		return err
	}

	for _, group := range groups {
		err := s.mergeDeviceGroup(group.UserID, group.StableDeviceID)
		if err != nil {
			ErrorLog(ErrorMsg{
				Error: err.Error(),
				Msg: fmt.Sprintf("合併設備組失敗 (UserID: %d, StableDeviceID: %s)",
					group.UserID, group.StableDeviceID[:8]+"..."),
			})
			continue
		}
	}

	return nil
}

// mergeDeviceGroup 合併特定用戶的同一物理設備記錄
func (s *DeviceCleanupService) mergeDeviceGroup(userID uint64, stableDeviceID string) error {
	var devices []DeviceRecord
	err := s.db.Where("user_id = ? AND stable_device_id = ?", userID, stableDeviceID).
		Order("last_seen_at DESC").Find(&devices).Error
	if err != nil {
		return err
	}

	if len(devices) <= 1 {
		return nil // 沒有重複記錄
	}

	// 保留最近活動的設備記錄
	keepDevice := devices[0]

	// 更新保留的設備記錄，合併信息
	for i := 1; i < len(devices); i++ {
		device := devices[i]

		// 如果舊記錄有更好的設備名稱，使用它
		if len(device.DeviceName) > len(keepDevice.DeviceName) {
			keepDevice.DeviceName = device.DeviceName
		}

		// 如果舊記錄有更高的可信度，使用它
		if device.Confidence > keepDevice.Confidence {
			keepDevice.Confidence = device.Confidence
		}

		// 如果舊記錄創建時間更早，保留創建時間
		if device.CreatedAt.Before(keepDevice.CreatedAt) {
			keepDevice.CreatedAt = device.CreatedAt
		}
	}

	// 更新保留的設備記錄
	err = s.db.Save(&keepDevice).Error
	if err != nil {
		return err
	}

	// 刪除重複的記錄
	for i := 1; i < len(devices); i++ {
		err = s.db.Delete(&devices[i]).Error
		if err != nil {
			ErrorLog(ErrorMsg{
				Error: err.Error(),
				Msg:   fmt.Sprintf("刪除重複設備記錄失敗 (ID: %d)", devices[i].ID),
			})
		}
	}

	return nil
}

// cleanupInactiveDevices 清理長期不活躍的設備記錄
func (s *DeviceCleanupService) cleanupInactiveDevices() error {
	// 刪除超過90天未活動的設備記錄
	cutoffTime := time.Now().AddDate(0, 0, -90)

	result := s.db.Where("last_seen_at < ? AND is_active = false", cutoffTime).Delete(&DeviceRecord{})
	if result.Error != nil {
		return result.Error
	}

	return nil
}

// GetDeviceStatistics 獲取設備統計信息
func (s *DeviceCleanupService) GetDeviceStatistics() (map[string]any, error) {
	stats := make(map[string]any)

	// 總設備數
	var totalDevices int64
	err := s.db.Model(&DeviceRecord{}).Count(&totalDevices).Error
	if err != nil {
		return nil, err
	}
	stats["total_devices"] = totalDevices

	// 活躍設備數
	var activeDevices int64
	err = s.db.Model(&DeviceRecord{}).Where("is_active = true").Count(&activeDevices).Error
	if err != nil {
		return nil, err
	}
	stats["active_devices"] = activeDevices

	// 有穩定設備ID的記錄數
	var devicesWithStableID int64
	err = s.db.Model(&DeviceRecord{}).Where("stable_device_id != '' AND stable_device_id IS NOT NULL").Count(&devicesWithStableID).Error
	if err != nil {
		return nil, err
	}
	stats["devices_with_stable_id"] = devicesWithStableID

	// 重複設備組數
	type DuplicateGroup struct {
		Count int `json:"count"`
	}
	var duplicateGroups []DuplicateGroup
	err = s.db.Model(&DeviceRecord{}).
		Select("COUNT(*) as count").
		Where("stable_device_id != '' AND stable_device_id IS NOT NULL").
		Group("user_id, stable_device_id").
		Having("COUNT(*) > 1").
		Find(&duplicateGroups).Error
	if err != nil {
		return nil, err
	}
	stats["duplicate_groups"] = len(duplicateGroups)

	// 平均可信度
	type AvgConfidence struct {
		Avg float64 `json:"avg"`
	}
	var avgConf AvgConfidence
	err = s.db.Model(&DeviceRecord{}).
		Select("AVG(confidence) as avg").
		Where("confidence > 0").
		Scan(&avgConf).Error
	if err != nil {
		return nil, err
	}
	stats["average_confidence"] = avgConf.Avg

	return stats, nil
}

// generateStableDeviceIDFromUserAgent 從User-Agent生成穩定設備ID
func generateStableDeviceIDFromUserAgent(userAgent string) string {
	// 重用 auth.go 中的邏輯
	return generateStableDeviceID(userAgent)
}

// 這些函數需要從 auth.go 中導入或重新實現
// 為了避免循環導入，這裡重新實現簡化版本

func generateStableDeviceID(userAgent string) string {
	hasher := md5.New()
	stableFeatures := extractStableFeatures(userAgent)
	hasher.Write([]byte(stableFeatures))
	return hex.EncodeToString(hasher.Sum(nil))
}

func extractStableFeatures(userAgent string) string {
	ua := strings.ToLower(userAgent)
	features := []string{}

	// 提取操作系統信息
	if strings.Contains(ua, "windows") {
		if match := regexp.MustCompile(`windows nt ([\d\.]+)`).FindStringSubmatch(ua); len(match) > 1 {
			features = append(features, "windows-"+match[1])
		} else {
			features = append(features, "windows")
		}
	} else if strings.Contains(ua, "mac os x") {
		if match := regexp.MustCompile(`mac os x ([\d_\.]+)`).FindStringSubmatch(ua); len(match) > 1 {
			features = append(features, "macos-"+strings.ReplaceAll(match[1], "_", "."))
		} else {
			features = append(features, "macos")
		}
	} else if strings.Contains(ua, "android") {
		if match := regexp.MustCompile(`android ([\d\.]+)`).FindStringSubmatch(ua); len(match) > 1 {
			features = append(features, "android-"+match[1])
		} else {
			features = append(features, "android")
		}
	} else if strings.Contains(ua, "iphone") || strings.Contains(ua, "ipad") {
		if match := regexp.MustCompile(`os ([\d_]+)`).FindStringSubmatch(ua); len(match) > 1 {
			features = append(features, "ios-"+strings.ReplaceAll(match[1], "_", "."))
		} else {
			features = append(features, "ios")
		}
	}

	return strings.Join(features, "|")
}
