<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { font-size: 18px; font-weight: bold; margin: 10px 0; }
        .messages { border: 1px solid #ccc; padding: 10px; height: 300px; overflow-y: auto; }
        .message { margin: 5px 0; padding: 5px; border-bottom: 1px solid #eee; }
        .error { color: red; }
        .success { color: green; }
        .info { color: blue; }
        button { margin: 5px; padding: 10px; }
    </style>
</head>
<body>
    <h1>WebSocket Connection Test</h1>

    <div>
        <input type="text" id="tokenInput" placeholder="請輸入access token" style="width: 400px;">
        <button onclick="connectWebSocket()">連接</button>
        <button onclick="disconnectWebSocket()">斷開</button>
        <button onclick="testHealth()">健康檢查</button>
        <button onclick="testHttpApi()">測試HTTP API</button>
    </div>

    <div id="status" class="status">未連接</div>
    <div id="messages" class="messages"></div>

    <script>
        let ws = null;

        function addMessage(message, type = 'info') {
            const messages = document.getElementById('messages');
            const div = document.createElement('div');
            div.className = `message ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            messages.appendChild(div);
            messages.scrollTop = messages.scrollHeight;
            console.log(message);
        }

        function connectWebSocket() {
            const token = document.getElementById('tokenInput').value.trim();
            if (!token) {
                addMessage('請輸入access token', 'error');
                return;
            }

            if (ws) {
                ws.close();
            }

            const wsUrl = `ws://127.0.0.1:8088/api/ws?token=${token}`;
            addMessage(`嘗試連接: ${wsUrl}`, 'info');

            document.getElementById('status').textContent = '連接中...';
            document.getElementById('status').style.color = 'orange';

            try {
                ws = new WebSocket(wsUrl);

                ws.onopen = function() {
                    document.getElementById('status').textContent = '✅ 連接成功';
                    document.getElementById('status').style.color = 'green';
                    addMessage('WebSocket連接成功', 'success');
                };

                ws.onmessage = function(event) {
                    addMessage(`收到消息: ${event.data}`, 'success');
                };

                ws.onclose = function(event) {
                    document.getElementById('status').textContent = `❌ 連接關閉 (${event.code})`;
                    document.getElementById('status').style.color = 'red';
                    addMessage(`WebSocket連接關閉: code=${event.code}, reason=${event.reason || '無'}`, 'error');
                };

                ws.onerror = function(error) {
                    document.getElementById('status').textContent = '❌ 連接錯誤';
                    document.getElementById('status').style.color = 'red';
                    addMessage('WebSocket連接錯誤', 'error');
                };
            } catch (error) {
                addMessage(`創建WebSocket失敗: ${error}`, 'error');
            }
        }

        function disconnectWebSocket() {
            if (ws) {
                ws.close(1000, 'Manual disconnect');
                ws = null;
                addMessage('手動斷開連接', 'info');
            }
        }

        async function testHealth() {
            try {
                addMessage('測試服務器健康狀態...', 'info');
                const response = await fetch('http://127.0.0.1:8088/api/health');

                if (response.ok) {
                    const data = await response.json();
                    addMessage(`服務器健康: ${JSON.stringify(data)}`, 'success');
                } else {
                    addMessage(`健康檢查失敗: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addMessage(`健康檢查錯誤: ${error}`, 'error');
            }
        }

        async function testHttpApi() {
            const token = document.getElementById('tokenInput').value.trim();
            if (!token) {
                addMessage('請輸入access token', 'error');
                return;
            }

            try {
                addMessage('測試HTTP API連接...', 'info');
                const response = await fetch('http://127.0.0.1:8088/api/profile', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    addMessage('HTTP API連接成功', 'success');
                } else {
                    addMessage(`HTTP API連接失敗: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addMessage(`HTTP API測試失敗: ${error}`, 'error');
            }
        }
    </script>
</body>
</html>
