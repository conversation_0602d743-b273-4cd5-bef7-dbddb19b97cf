package controllers

import (
	"fmt"
	"log"
	. "lottery/database"
	. "lottery/models"
	. "lottery/utils"
	"time"

	"gorm.io/gorm/clause"
)

func CheckTWLotto(lottoResult interface{}, lottoType LottoType) {
	var latestDrawDate string

	conn := ConnectDB()
	defer CloseDB(conn)

	// 取得最新開獎日期
	if err := getLatestLottoDrawDate(lottoResult, &latestDrawDate); err != nil {
		ErrorLog(ErrorMsg{
			Msg:   "查詢最新開獎日期失敗",
			Error: err.Error(),
		})
		return
	}

	// 如果沒有資料，從指定月份開始
	var startTime time.Time
	if latestDrawDate == "" {
		var err error
		startTime, err = time.Parse("2006-01", SuperLottoMonth)
		if err != nil {
			ErrorLog(ErrorMsg{
				Msg:   "解析起始月份失敗",
				Error: err.<PERSON><PERSON>r(),
			})
			return
		}
	} else {
		var err error
		startTime, err = time.Parse("2006-01-02", latestDrawDate)
		if err != nil {
			ErrorLog(ErrorMsg{
				Msg:   "解析最新開獎日期失敗",
				Error: err.Error(),
			})
			return
		}
		// 從隔天開始抓取
		startTime = startTime.AddDate(0, 0, 1)
	}

	// 確保不會抓取超過當前時間
	currentTime := time.Now()
	if !startTime.Before(currentTime) {
		InfoLog("資料已是最新，無需更新")
		return
	}

	client := NewTaiwanLotteryClient().SetLottoType(lottoType)

	for currentMonth := startTime; currentMonth.Before(currentTime) || isSameMonth(currentMonth, currentTime); currentMonth = currentMonth.AddDate(0, 1, 0) {
		year := currentMonth.Year()
		month := int(currentMonth.Month())

		InfoLog(fmt.Sprintf("正在抓取 %d-%02d 的開獎資料", year, month))

		if err := client.SetMonth(fmt.Sprintf("%d-%02d", year, month)).GetResult(); err != nil {
			ErrorLog(ErrorMsg{
				Msg:   fmt.Sprintf("取得 %d-%02d 開獎結果失敗", year, month),
				Error: err.Error(),
			})
			// 繼續處理下個月，不要中斷整個流程
			continue
		}

		// 檢查是否有獲得資料
		if len(client.Results) == 0 {
			InfoLog(fmt.Sprintf("%d-%02d 沒有開獎資料", year, month))
			continue
		}

		// 依draw date排序日期
		sortedResults := sortLottoResults(client.Results)

		// 使用交易確保資料一致性
		tx := conn.Begin()
		successCount := 0

		for _, r := range sortedResults {
			data := map[string]interface{}{
				"period":             r.Period,
				"draw_date":          r.DrawDate,
				"draw_number_size":   r.DrawNumberSize,
				"draw_number_appear": r.DrawNumberAppear,
			}
			assignmentCols := []string{"draw_number_size", "draw_number_appear"}

			if r.SpecialNumber != 0 {
				data["special_number"] = r.SpecialNumber
				assignmentCols = append(assignmentCols, "special_number")
			}

			// 改善衝突處理：更新所有欄位
			result := tx.Model(&lottoResult).Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "period"}, {Name: "draw_date"}},
				DoUpdates: clause.AssignmentColumns(assignmentCols),
			}).Create(&data)

			if result.Error != nil {
				ErrorLog(ErrorMsg{
					Msg:   fmt.Sprintf("儲存期數 %d 開獎結果失敗", r.Period),
					Error: result.Error.Error(),
				})
				tx.Rollback()
				break
			}
			successCount++
		}

		if successCount == len(sortedResults) {
			tx.Commit()
			InfoLog(fmt.Sprintf("成功儲存 %d-%02d 的 %d 筆開獎資料", year, month, successCount))
		} else {
			tx.Rollback()
			ErrorLog(ErrorMsg{
				Msg: fmt.Sprintf("%d-%02d 資料儲存不完整，已回滾", year, month),
			})
		}

		// 避免請求過於頻繁
		time.Sleep(500 * time.Millisecond)
	}
}

// 檢查是否為同一個月
func isSameMonth(t1, t2 time.Time) bool {
	return t1.Year() == t2.Year() && t1.Month() == t2.Month()
}

func InfoLog(msg string) {
	// 實作資訊日誌記錄
	log.Printf("[INFO] %s", msg)
}

// SortLottoResponse 依期數排序
// 日期越早的期數越小
func sortLottoResults(result []LottoResult) []LottoResult {
	for i := 0; i < len(result); i++ {
		for j := i + 1; j < len(result); j++ {
			if result[i].Period > (result)[j].Period {
				result[i], result[j] = result[j], result[i]
			}
		}
	}

	return result
}

func CheckHKLotto() {
	var latestDrawDate string

	conn := ConnectDB()
	defer CloseDB(conn)

	if err := getLatestLottoDrawDate(LottoHKResult{}, &latestDrawDate); err != nil {
		ErrorLog(ErrorMsg{
			Msg:   "查詢最新開獎日期失敗",
			Error: err.Error(),
		})
		return
	}

	if latestDrawDate == "" {
		latestDrawDate = fmt.Sprintf("%s-01", LottoHKMonth)
	}

	t, _ := time.Parse("2006-01-02", latestDrawDate)
	if t.Before(time.Now()) {
		client := NewHKLotteryCpzhanClient()
		for {
			year := t.Year()

			if year > time.Now().Year() {
				break
			}

			if err := client.SetYear(year).GetResult(); err != nil {
				ErrorLog(ErrorMsg{
					Msg:   "取得開獎結果失敗",
					Error: err.Error(),
				})
				break
			}

			for _, r := range client.Results {
				if err := conn.Model(&LottoHKResult{}).Clauses(
					clause.OnConflict{
						Columns:   []clause.Column{{Name: "period"}, {Name: "draw_date"}},
						DoUpdates: clause.AssignmentColumns([]string{"draw_number_size", "draw_number_appear", "special_number"}),
					}).Create(&r).Error; err != nil {
					ErrorLog(ErrorMsg{
						Msg:   "儲存開獎結果失敗",
						Error: err.Error(),
					})
					break
				}
			}

			t = t.AddDate(1, 0, 0)
		}
	}
}

// 美國加州天天樂
func CheckCaliforniaLotto() {
	var latestDrawDate string

	conn := ConnectDB()
	defer CloseDB(conn)

	// 取得最新開獎日期
	if err := getLatestLottoDrawDate(LottoCaliforniaResult{}, &latestDrawDate); err != nil {
		ErrorLog(ErrorMsg{
			Msg:   "查詢最新開獎日期失敗",
			Error: err.Error(),
		})
		return
	}

	// 如果沒有資料，從指定月份開始
	if latestDrawDate == "" {
		latestDrawDate = fmt.Sprintf("%s-01", CaLottoMonth)
	}

	t, _ := time.Parse("2006-01-02", latestDrawDate)
	if t.Before(time.Now()) {
		client := NewCaLotteryClient()
		for {
			year := t.Year()

			if year > time.Now().Year() {
				break
			}

			InfoLog(fmt.Sprintf("正在抓取 %d 年的加州天天樂開獎資料", year))

			if err := client.SetYear(year).GetResult(); err != nil {
				ErrorLog(ErrorMsg{
					Msg:   fmt.Sprintf("取得 %d 年開獎結果失敗", year),
					Error: err.Error(),
				})
				break
			}

			// 檢查是否有獲得資料
			if len(client.Results) == 0 {
				InfoLog(fmt.Sprintf("%d 年沒有開獎資料", year))
				t = t.AddDate(1, 0, 0)
				continue
			}

			// 使用交易確保資料一致性
			tx := conn.Begin()
			successCount := 0

			for _, r := range client.Results {
				if err := tx.Model(&LottoCaliforniaResult{}).Clauses(
					clause.OnConflict{
						Columns:   []clause.Column{{Name: "period"}, {Name: "draw_date"}},
						DoUpdates: clause.AssignmentColumns([]string{"draw_number_size", "draw_number_appear"}),
					}).Omit("special_number").Create(&r).Error; err != nil {
					ErrorLog(ErrorMsg{
						Msg:   fmt.Sprintf("儲存期數 %d 開獎結果失敗", r.Period),
						Error: err.Error(),
					})
					tx.Rollback()
					break
				}
				successCount++
			}

			if successCount == len(client.Results) {
				tx.Commit()
				InfoLog(fmt.Sprintf("成功儲存 %d 年的 %d 筆開獎資料", year, successCount))
			} else {
				tx.Rollback()
				ErrorLog(ErrorMsg{
					Msg: fmt.Sprintf("%d 年資料儲存不完整，已回滾", year),
				})
			}

			// 避免請求過於頻繁
			time.Sleep(500 * time.Millisecond)

			t = t.AddDate(1, 0, 0)
		}
	}
}

func getLatestLottoDrawDate(lottoResult interface{}, latestDrawDate *string) error {
	conn := ConnectDB()
	defer CloseDB(conn)

	if err := conn.Model(&lottoResult).Select("DATE_FORMAT(`draw_date`, '%Y-%m-%d')").
		Order("draw_date DESC").Limit(1).Scan(latestDrawDate).Error; err != nil {
		return err
	}

	return nil
}
