package controllers

import (
	"fmt"
	"net/http"

	. "lottery/models"
	. "lottery/utils"

	"github.com/PuerkitoBio/goquery"
	querystring "github.com/google/go-querystring/query"
)

type HKLotteryCpzhanClient struct {
	Params  HKLotteryRequestParams `json:"params"`
	Results []LottoHKResult        `json:"results"`
}

func NewHKLotteryCpzhanClient() *HKLotteryCpzhanClient {
	client := &HKLotteryCpzhanClient{
		Params: HKLotteryRequestParams{
			Year: 2002,
			Sort: Seq,
		},
	}

	return client
}

func (client *HKLotteryCpzhanClient) SetYear(year int) *HKLotteryCpzhanClient {
	client.Params.Year = year
	return client
}

func (client *HKLotteryCpzhanClient) GetResult() error {
	err := client.request()

	return err
}

func (client *HKLotteryCpzhanClient) request() error {
	var results []LottoHKResult

	for index, sort := range []CpzhanSortType{Seq, Cmp} {
		client.Params.Sort = sort
		// get html content
		res, err := client.getHtmlContent()
		if err != nil {
			return err
		}
		defer res.Body.Close()

		// Load the HTML document
		doc, err := goquery.NewDocumentFromReader(res.Body)
		if err != nil {
			return err
		}

		// 年份、期數、開獎日期、N1、N2、N3、N4、N5、N6、特碼
		doc.Find(".mytable table tbody tr").Each(func(i int, s *goquery.Selection) {
			var (
				r            LottoHKResult
				year, period string
			)
			s.Find("td").Each(func(j int, s *goquery.Selection) {
				switch j {
				case 0:
					year = s.Text()
				case 1:
					period = s.Text()
				case 2:
					r.DrawDate = s.Text()
				case 3, 4, 5, 6, 7, 8:
					if client.Params.Sort == Seq {
						r.DrawNumberAppear = append(r.DrawNumberAppear, StrToInt(s.Text()))
					} else {
						r.DrawNumberSize = append(r.DrawNumberSize, StrToInt(s.Text()))
					}
				case 9:
					r.SpecialNumber = StrToInt(s.Text())
				}
			})
			r.Period = StrToInt(fmt.Sprintf("%02s%04s", year[2:], period))

			if index == 0 {
				results = append(results, r)
			} else {
				results[i].DrawNumberSize = r.DrawNumberSize
			}
		})
	}

	client.Results = results

	return nil
}

func (client *HKLotteryCpzhanClient) getHtmlContent() (*http.Response, error) {
	requestUrl := HKLotteryCpzhanUrl
	q, err := querystring.Values(client.Params)
	if err != nil {
		return nil, err
	}

	if len(q) > 0 {
		requestUrl += "?" + q.Encode()
	}

	req, err := http.NewRequest(http.MethodGet, requestUrl, nil)
	if err != nil {
		return nil, err
	}

	c := &http.Client{}
	res, err := c.Do(req)
	if err != nil {
		return nil, err
	}

	if res.StatusCode > http.StatusIMUsed {
		return nil, fmt.Errorf("request failed: %s", res.Status)
	}

	return res, nil
}
