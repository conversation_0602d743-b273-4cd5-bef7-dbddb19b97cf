import { defineStore } from 'pinia';
import { Dialog } from 'quasar';
import { LoginResponse, TokenResponse, User } from '@/api/modules/auth';

// WebSocket連接
let ws: WebSocket | null = null;

// Token驗證定時器
let tokenValidationTimer: NodeJS.Timeout | null = null;

// WebSocket重連定時器
let wsReconnectTimer: NodeJS.Timeout | null = null;

// WebSocket連接狀態
let wsConnectionState: 'disconnected' | 'connecting' | 'connected' | 'failed' = 'disconnected';

// WebSocket重連次數
let wsReconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 5;
const RECONNECT_INTERVAL = 3000; // 3秒

export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: null as User | null,
    accessToken: '',
    tokenExpiry: null as Date | null,
    refreshToken: '',
    refreshTokenExpiry: null as Date | null,
    // 添加一個標記來避免循環更新
    _isUpdatingFromStorage: false,
  }),
  persist: {
    // 使用 pinia-plugin-persistedstate 的配置
    key: 'auth-store',
    storage: localStorage,
    // 只持久化必要的字段，避免與手動 localStorage 操作衝突
    pick: ['user'],
  },
  getters: {
    userInfo: (state) => state.user,
    isAuthenticated: (state) => !!state.accessToken,
    isTokenExpired: (state) => {
      if (!state.tokenExpiry) {
        return true;
      }

      return new Date() >= state.tokenExpiry;
    },
    isRefreshTokenExpired: (state) => {
      if (!state.refreshTokenExpiry) {
        return true;
      }

      return new Date() >= state.refreshTokenExpiry;
    },
    // 檢查是否有本地token（不驗證有效性）
    hasLocalToken: (state) => !!state.accessToken,
  },
  actions: {
    // 初始化跨分頁同步監聽器
    initCrossTabSync() {
      // 監聽 storage 事件，實現跨分頁同步
      window.addEventListener('storage', (event) => {
        if (this._isUpdatingFromStorage) return;

        // 監聽 token 更新事件
        if (event.key === 'auth-token-sync' && event.newValue) {
          try {
            const tokenData = JSON.parse(event.newValue);
            this._isUpdatingFromStorage = true;
            this.accessToken = tokenData.access_token;
            this.tokenExpiry = new Date(tokenData.expires_at);
            this.refreshToken = tokenData.refresh_token;
            this.refreshTokenExpiry = new Date(tokenData.refresh_expires_at);
            this._isUpdatingFromStorage = false;
          } catch (error) {
            console.error('Failed to sync token from storage:', error);
          }
        }

        // 監聽登出事件
        if (event.key === 'auth-logout-sync' && event.newValue === 'true') {
          this._isUpdatingFromStorage = true;
          this.user = null;
          this.accessToken = '';
          this.tokenExpiry = null;
          this.refreshToken = '';
          this.refreshTokenExpiry = null;
          this._isUpdatingFromStorage = false;

          // 跳轉到登入頁面
          window.location.href = '/login';
        }
      });
    },

    // 初始化應用時檢查登入狀態並建立WebSocket連接
    initializeApp() {
      // 從localStorage恢復token狀態
      const accessToken = localStorage.getItem('accessToken');
      const tokenExpiry = localStorage.getItem('tokenExpiry');
      const refreshToken = localStorage.getItem('refreshToken');
      const refreshTokenExpiry = localStorage.getItem('refreshTokenExpiry');

      if (accessToken && tokenExpiry && refreshToken && refreshTokenExpiry) {
        this.accessToken = accessToken;
        this.tokenExpiry = new Date(tokenExpiry);
        this.refreshToken = refreshToken;
        this.refreshTokenExpiry = new Date(refreshTokenExpiry);

        // 如果用戶已登入且token未過期，建立WebSocket連接
        if (this.hasLocalToken && !this.isTokenExpired) {
          console.log('檢測到有效的登入狀態，建立WebSocket連接');
          this.setupWebSocket();
        }
      }

      // 監聽頁面可見性變化，確保WebSocket連接
      document.addEventListener('visibilitychange', () => {
        if (!document.hidden && this.hasLocalToken && !this.isTokenExpired) {
          // 頁面變為可見且用戶已登入，檢查WebSocket連接
          if (!ws || ws.readyState !== WebSocket.OPEN) {
            console.log('頁面可見，重新建立WebSocket連接');
            this.setupWebSocket();
          }
        }
      });

      // 啟動定期token驗證
      this.startTokenValidation();
    },

    // 啟動定期token驗證
    startTokenValidation() {
      // 清除現有定時器
      if (tokenValidationTimer) {
        clearInterval(tokenValidationTimer);
      }

      // 每5分鐘驗證一次token
      tokenValidationTimer = setInterval(async () => {
        if (this.hasLocalToken && !this.isTokenExpired) {
          const isValid = await this.validateToken();
          if (!isValid) {
            console.log('定期驗證發現token已失效');
            // token失效會在validateToken中自動處理登出
          }
        }
      }, 5 * 60 * 1000); // 5分鐘
    },

    // 停止定期token驗證
    stopTokenValidation() {
      if (tokenValidationTimer) {
        clearInterval(tokenValidationTimer);
        tokenValidationTimer = null;
      }
    },

    // 驗證token是否在後端仍然有效
    async validateToken(): Promise<boolean> {
      if (!this.accessToken) {
        return false;
      }

      try {
        // 嘗試獲取用戶資料來驗證token
        const response = await fetch('/api/profile', {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`
          }
        });

        if (response.ok) {
          // token有效，更新用戶資料
          const userData = await response.json();
          this.user = userData;
          return true;
        } else if (response.status === 401) {
          // token無效，嘗試使用refresh token刷新
          console.log('Token已失效，嘗試刷新token');

          if (!this.isRefreshTokenExpired) {
            try {
              console.log('🔄 使用refresh token刷新access token');
              const AUTH_API = (await import('@/api/modules/auth')).default;
              const response = await AUTH_API.refreshToken();
              this.updateToken(response.data);
              console.log('✅ Token刷新成功');
              return true;
            } catch (error) {
              console.error('❌ 刷新token失敗:', error);
              console.log('Refresh token也已失效，執行登出');
              this.logout();
              return false;
            }
          } else {
            console.log('Refresh token已過期，執行登出');
            this.logout();
            return false;
          }
        } else {
          // 其他錯誤，暫時認為token有效
          console.warn('Token驗證返回非401錯誤:', response.status);
          return true;
        }
      } catch (error) {
        console.error('驗證token時發生錯誤:', error);
        // 網絡錯誤等情況，暫時認為token有效
        return true;
      }
    },

    // 檢查是否已認證（包含後端驗證）
    async checkAuthentication(): Promise<boolean> {
      // 首先檢查本地是否有token
      if (!this.hasLocalToken) {
        return false;
      }

      // 如果本地token已過期且refresh token也過期，直接登出
      if (this.isTokenExpired && this.isRefreshTokenExpired) {
        console.log('Access token和refresh token都已過期，執行登出');
        this.logout();
        return false;
      }

      // 驗證token在後端是否仍然有效（包含自動刷新邏輯）
      return await this.validateToken();
    },

    login(data: LoginResponse) {
      this.user = data.user;
      this.updateToken(data);

      // 登入成功後立即強制重新建立WebSocket連接
      setTimeout(() => {
        this.forceReconnectWebSocket();
      }, 100); // 短暫延遲確保token已保存
    },
    getTokenClaims() {
      const accessToken = this.accessToken;
      if (!accessToken) {
        return null;
      }

      try {
        // 解碼 accessToken
        const base64Url = accessToken.split('.')[1];
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        const claims = JSON.parse(atob(base64));

        return {
          user_id: claims.user_id,
          is_admin: claims.is_admin,
        };
      } catch {
        return null;
      }
    },
    isAdmin() {
      const claims = this.getTokenClaims();
      return claims?.is_admin || false;
    },
    updateToken(data: TokenResponse) {
      // 避免在同步過程中觸發循環更新
      if (this._isUpdatingFromStorage) return;

      this.accessToken = data.access_token;
      this.tokenExpiry = data.expires_at;
      this.refreshToken = data.refresh_token;
      this.refreshTokenExpiry = data.refresh_expires_at;

      // 更新 localStorage（保持向後兼容）
      localStorage.setItem('accessToken', data.access_token);
      localStorage.setItem('tokenExpiry', data.expires_at.toString());
      localStorage.setItem('refreshToken', data.refresh_token);
      localStorage.setItem(
        'refreshTokenExpiry',
        data.refresh_expires_at.toString()
      );

      // 觸發跨分頁同步事件
      this.broadcastTokenUpdate(data);
    },

    // 廣播 token 更新到其他分頁
    broadcastTokenUpdate(data: TokenResponse) {
      try {
        // 使用臨時的 localStorage 項目來觸發 storage 事件
        const syncData = {
          access_token: data.access_token,
          expires_at: data.expires_at,
          refresh_token: data.refresh_token,
          refresh_expires_at: data.refresh_expires_at,
          timestamp: Date.now()
        };

        localStorage.setItem('auth-token-sync', JSON.stringify(syncData));
        // 立即移除，避免污染 localStorage
        setTimeout(() => {
          localStorage.removeItem('auth-token-sync');
        }, 100);
      } catch (error) {
        console.error('Failed to broadcast token update:', error);
      }
    },
    logout() {
      // 避免在同步過程中觸發循環更新
      if (this._isUpdatingFromStorage) return;

      // 停止定期token驗證
      this.stopTokenValidation();

      // 清理WebSocket相關資源
      if (wsReconnectTimer) {
        clearTimeout(wsReconnectTimer);
        wsReconnectTimer = null;
      }
      wsReconnectAttempts = 0;
      wsConnectionState = 'disconnected';

      // 關閉WebSocket連接
      if (ws) {
        ws.close(1000, 'User logout'); // 正常關閉
        ws = null;
      }

      this.accessToken = '';
      this.tokenExpiry = null;
      this.refreshToken = '';
      this.refreshTokenExpiry = null;
      this.user = null;

      localStorage.removeItem('accessToken');
      localStorage.removeItem('tokenExpiry');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('refreshTokenExpiry');

      // 觸發跨分頁登出同步事件
      this.broadcastLogout();
    },

    // 廣播登出事件到其他分頁
    broadcastLogout() {
      try {
        localStorage.setItem('auth-logout-sync', 'true');
        // 立即移除，避免污染 localStorage
        setTimeout(() => {
          localStorage.removeItem('auth-logout-sync');
        }, 100);
      } catch (error) {
        console.error('Failed to broadcast logout:', error);
      }
    },
    // 從 localStorage 初始化 token 數據（用於向後兼容）
    initializeFromStorage() {
      const accessToken = localStorage.getItem('accessToken');
      const refreshToken = localStorage.getItem('refreshToken');
      const tokenExpiry = localStorage.getItem('tokenExpiry');
      const refreshTokenExpiry = localStorage.getItem('refreshTokenExpiry');

      if (accessToken && refreshToken && tokenExpiry && refreshTokenExpiry) {
        this.accessToken = accessToken;
        this.tokenExpiry = new Date(tokenExpiry);
        this.refreshToken = refreshToken;
        this.refreshTokenExpiry = new Date(refreshTokenExpiry);
      }
    },
    async setupWebSocket(isRetry = false) {
      // 如果正在連接中，避免重複連接
      if (wsConnectionState === 'connecting') {
        // WebSocket正在連接中，跳過重複連接
        return;
      }

      // 關閉現有連接
      if (ws) {
        ws.close();
        ws = null;
      }

      // 清除重連定時器
      if (wsReconnectTimer) {
        clearTimeout(wsReconnectTimer);
        wsReconnectTimer = null;
      }

      // 檢查是否有有效的token
      const token = this.accessToken || localStorage.getItem('accessToken');
      if (!token) {
        // 沒有有效的token，跳過WebSocket連接
        wsConnectionState = 'disconnected';
        return;
      }

      // 檢查重連次數限制
      if (isRetry && wsReconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
        // WebSocket重連次數已達上限 (${MAX_RECONNECT_ATTEMPTS})，停止重連
        wsConnectionState = 'failed';
        return;
      }

      // 建立WebSocket連接
      let wsUrl = import.meta.env.VITE_WS_URL;

      // 如果沒有設置環境變數，根據當前環境自動判斷
      if (!wsUrl) {
        if (import.meta.env.DEV) {
          // 開發環境：使用開發服務器的代理
          const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
          wsUrl = `${protocol}//${window.location.host}/api/ws`;
        } else {
          // 生產環境：直接連接到後端服務器
          const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
          const host = window.location.hostname;
          const port = '8088';
          wsUrl = `${protocol}//${host}:${port}/api/ws`;
        }
      }

      wsConnectionState = 'connecting';
      if (isRetry) {
        wsReconnectAttempts++;
        // 🔄 WebSocket重連嘗試 ${wsReconnectAttempts}/${MAX_RECONNECT_ATTEMPTS}: ${wsUrl}
      } else {
        wsReconnectAttempts = 0;
        // 🔗 嘗試建立WebSocket連接:', wsUrl
      }

      try {
        // 嘗試獲取設備ID
        let deviceIdParam = '';
        try {
          const { getLoginDeviceId } = await import('@/services/device-identification');
          const deviceId = await getLoginDeviceId();
          deviceIdParam = `&device_id=${encodeURIComponent(deviceId)}`;
          console.log('🔗 WebSocket使用設備ID:', deviceId.substring(0, 8) + '...');
        } catch (error) {
          console.warn('獲取設備ID失敗，使用傳統連接方式:', error);
        }

        ws = new WebSocket(`${wsUrl}?token=${token}${deviceIdParam}`);

        // 連接超時檢測
        const connectionTimeout = setTimeout(() => {
          if (wsConnectionState === 'connecting') {
            console.log('⏰ WebSocket連接超時');
            wsConnectionState = 'failed';
            if (ws) {
              ws.close();
            }
            this.scheduleWebSocketReconnect();
          }
        }, 10000); // 10秒超時

        ws.onopen = () => {
          clearTimeout(connectionTimeout);
          wsConnectionState = 'connected';
          wsReconnectAttempts = 0; // 重置重連次數
          console.log('✅ WebSocket連接已建立');

          // 發送心跳包確認連接
          if (ws && ws.readyState === WebSocket.OPEN) {
            try {
              ws.send(JSON.stringify({ type: 'ping' }));
            } catch (error) {
              console.error('發送心跳包失敗:', error);
            }
          }
        };

        ws.onmessage = (event: MessageEvent) => {
          try {
            console.log('📨 收到WebSocket消息:', event.data);
            const data = JSON.parse(event.data);

            if (data.type === 'forced_logout') {
              console.log('🚨 收到強制登出通知:', data.message);

              // 立即關閉WebSocket連接
              if (ws) {
                ws.close(1000, 'Forced logout');
                ws = null;
              }
              wsConnectionState = 'disconnected';

              // 使用Dialog顯示強制登出消息
              Dialog.create({
                title: '帳號安全提醒',
                message: data.message || '您的帳號已在另一個設備登入，將自動登出',
                persistent: true,
                ok: {
                  label: '確定',
                  color: 'primary'
                }
              }).onOk(() => {
                // 執行登出操作
                this.logout();

                // 跳轉到登入頁面
                window.location.href = '/login';
              });
            } else if (data.type === 'ping') {
              // 回應心跳包
              if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({ type: 'pong' }));
              }
            }
          } catch (error) {
            console.error('處理WebSocket消息時發生錯誤:', error);
          }
        };

        ws.onclose = (event) => {
          clearTimeout(connectionTimeout);
          console.log('❌ WebSocket連接已關閉:', event.code, event.reason);
          ws = null;

          // 更新連接狀態
          if (event.code === 1000) {
            wsConnectionState = 'disconnected'; // 正常關閉
          } else {
            wsConnectionState = 'failed'; // 異常關閉
          }

          // 如果不是正常關閉且用戶仍然登入，嘗試重新連接
          if (event.code !== 1000 && this.hasLocalToken && !this.isTokenExpired) {
            this.scheduleWebSocketReconnect();
          }
        };

        ws.onerror = (error) => {
          clearTimeout(connectionTimeout);
          console.error('❌ WebSocket連接錯誤:', error);
          wsConnectionState = 'failed';
          this.scheduleWebSocketReconnect();
        };
      } catch (error) {
        console.error('❌ 建立WebSocket連接失敗:', error);
        wsConnectionState = 'failed';
        this.scheduleWebSocketReconnect();
      }
    },

    // 調度WebSocket重連
    scheduleWebSocketReconnect() {
      // 檢查是否應該重連
      if (!this.hasLocalToken || this.isTokenExpired || wsReconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
        return;
      }

      // 清除現有的重連定時器
      if (wsReconnectTimer) {
        clearTimeout(wsReconnectTimer);
      }

      // 計算重連延遲（指數退避）
      const delay = Math.min(RECONNECT_INTERVAL * Math.pow(2, wsReconnectAttempts), 30000); // 最大30秒

      console.log(`🔄 ${delay}ms後嘗試重新連接WebSocket...`);
      wsReconnectTimer = setTimeout(() => {
        if (this.hasLocalToken && !this.isTokenExpired) {
          this.setupWebSocket(true);
        }
      }, delay);
    },

    // 強制重連WebSocket（重置重連次數）
    forceReconnectWebSocket() {
      wsReconnectAttempts = 0;
      wsConnectionState = 'disconnected';
      if (wsReconnectTimer) {
        clearTimeout(wsReconnectTimer);
        wsReconnectTimer = null;
      }
      this.setupWebSocket();
    },

    // 獲取WebSocket連接狀態
    getWebSocketState() {
      return {
        state: wsConnectionState,
        reconnectAttempts: wsReconnectAttempts,
        maxAttempts: MAX_RECONNECT_ATTEMPTS,
        isConnected: wsConnectionState === 'connected' && ws?.readyState === WebSocket.OPEN
      };
    },
  },
});

export type AuthStore = ReturnType<typeof useAuthStore>;
