<template>
  <q-page padding>
    <div class="q-pa-md">
      <h4>WebSocket 調試頁面</h4>

      <div class="row q-gutter-md">
        <!-- 連接狀態 -->
        <div class="col-12 col-md-6">
          <q-card>
            <q-card-section>
              <div class="text-h6">連接狀態</div>
              <div :class="connectionStatusClass">{{ connectionStatus }}</div>
              <div class="q-mt-sm">
                <div><strong>用戶:</strong> {{ authStore.user?.name || '未登入' }}</div>
                <div><strong>Token:</strong> {{ authStore.accessToken ? '已設置' : '未設置' }}</div>
                <div><strong>WebSocket狀態:</strong> {{ wsState.state }}</div>
                <div><strong>重連次數:</strong> {{ wsState.reconnectAttempts }}/{{ wsState.maxAttempts }}</div>
                <div><strong>已連接:</strong> {{ wsState.isConnected ? '是' : '否' }}</div>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <!-- 操作按鈕 -->
        <div class="col-12 col-md-6">
          <q-card>
            <q-card-section>
              <div class="text-h6">操作</div>
              <div class="q-gutter-sm">
                <q-btn color="primary" label="強制重連" @click="forceReconnect" />
                <q-btn color="secondary" label="發送心跳" @click="sendPing" />
                <q-btn color="orange" label="清除日誌" @click="clearLogs" />
                <q-btn color="red" label="模擬登出" @click="simulateLogout" />
              </div>
            </q-card-section>
          </q-card>
        </div>
      </div>

      <!-- 日誌顯示 -->
      <div class="q-mt-md">
        <q-card>
          <q-card-section>
            <div class="text-h6">WebSocket 日誌</div>
            <div class="q-mt-sm" style="max-height: 400px; overflow-y: auto;">
              <div v-for="(log, index) in logs" :key="index" class="q-mb-xs">
                <span class="text-caption text-grey">{{ log.timestamp }}</span>
                <span :class="getLogClass(log.type)">{{ log.message }}</span>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>

      <!-- 測試說明 -->
      <div class="q-mt-md">
        <q-card>
          <q-card-section>
            <div class="text-h6">測試步驟</div>
            <ol class="q-pl-md">
              <li>在此頁面登入並確認WebSocket連接成功</li>
              <li>在另一個瀏覽器/無痕模式用同一帳號登入</li>
              <li>觀察此頁面是否收到強制登出通知</li>
              <li>確認登出後再次登入是否能正常建立WebSocket連接</li>
            </ol>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useAuthStore } from '@/stores/auth';

const authStore = useAuthStore();

interface LogEntry {
  timestamp: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
}

const logs = ref<LogEntry[]>([]);
const connectionStatus = ref('檢查中...');

// WebSocket狀態
const wsState = computed(() => authStore.getWebSocketState());

// 連接狀態樣式
const connectionStatusClass = computed(() => {
  if (wsState.value.isConnected) {
    return 'text-green text-weight-bold';
  } else if (wsState.value.state === 'connecting') {
    return 'text-orange text-weight-bold';
  } else {
    return 'text-red text-weight-bold';
  }
});

// 添加日誌
const addLog = (message: string, type: LogEntry['type'] = 'info') => {
  const timestamp = new Date().toLocaleTimeString();
  logs.value.push({ timestamp, message, type });

  // 保持最多100條日誌
  if (logs.value.length > 100) {
    logs.value.shift();
  }

  console.log(`[${type.toUpperCase()}] ${message}`);
};

// 獲取日誌樣式
const getLogClass = (type: LogEntry['type']) => {
  switch (type) {
    case 'success': return 'text-green';
    case 'warning': return 'text-orange';
    case 'error': return 'text-red';
    default: return 'text-blue';
  }
};

// 更新連接狀態
const updateConnectionStatus = () => {
  const state = wsState.value;
  if (state.isConnected) {
    connectionStatus.value = '已連接';
  } else if (state.state === 'connecting') {
    connectionStatus.value = '連接中...';
  } else if (state.state === 'failed') {
    connectionStatus.value = `連接失敗 (${state.reconnectAttempts}/${state.maxAttempts})`;
  } else {
    connectionStatus.value = '未連接';
  }
};

// 強制重連
const forceReconnect = () => {
  addLog('手動觸發WebSocket強制重連', 'info');
  authStore.forceReconnectWebSocket();
};

// 發送心跳
const sendPing = () => {
  addLog('發送心跳包測試', 'info');
  // 這裡可以添加發送心跳的邏輯
};

// 清除日誌
const clearLogs = () => {
  logs.value = [];
  addLog('日誌已清除', 'info');
};

// 模擬登出
const simulateLogout = () => {
  addLog('模擬強制登出', 'warning');
  authStore.logout();
};

// 監聽WebSocket狀態變化
let statusInterval: NodeJS.Timeout;

onMounted(() => {
  addLog('WebSocket調試頁面已載入', 'success');

  // 定期更新狀態
  statusInterval = setInterval(() => {
    updateConnectionStatus();
  }, 1000);

  // 初始狀態檢查
  updateConnectionStatus();

  if (authStore.hasLocalToken) {
    addLog('檢測到本地token，檢查WebSocket連接狀態', 'info');
  } else {
    addLog('未檢測到本地token，請先登入', 'warning');
  }
});

onUnmounted(() => {
  if (statusInterval) {
    clearInterval(statusInterval);
  }
});
</script>

<style scoped>
.q-card {
  margin-bottom: 16px;
}
</style>
