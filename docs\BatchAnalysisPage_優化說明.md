# BatchAnalysisPage 版路分析優化說明

## 已完成的優化項目

### 1. 篩選條件功能
✅ **準確次數篩選**
- 新增準確次數選擇器（準過1次以上 ~ 準過5次以上）
- **移動到參數設定區域**：在版路分析參數設定中顯示
- 在產生報表前進行設定，影響分析結果的統計和Excel匯出

✅ **準確率篩選**
- 新增準確率選擇器（50% ~ 100%）
- **移動到參數設定區域**：與準確次數篩選一起顯示
- 與準確次數篩選同時作用，確保只匯出符合條件的分析結果

### 2. Excel多分頁功能
✅ **版路分析Excel結構**
現在版路分析會產生5個分頁：

#### 分頁1：預測號碼
- **每期分析結果**：顯示每一次分析的預測號碼
- **按出現次數排序**：每期的預測號碼按出現次數由高到低排序
- **格式**：日期 | 號碼1 | 號碼2 | ... （每10個號碼換列）
- **對齊顯示**：超過10個號碼時自動換列並對齊前一列
- **時間排序**：由舊到新排列
- **命中標示**：與實際開獎號碼相符的預測號碼後加★標記

#### 分頁2：未出現號碼-預測次數
- **每期分析結果**：顯示每一次分析的未出現號碼
- **依預測次數排序**：每期按預測次數排序的未出現號碼
- **格式**：日期 | 號碼1 | 號碼2 | ... （每10個號碼換列）
- **對齊顯示**：超過10個號碼時自動換列並對齊前一列
- **時間排序**：由舊到新排列
- **命中標示**：與實際開獎號碼相符的號碼後加★標記

#### 分頁3：未出現號碼-大小排序
- **每期分析結果**：顯示每一次分析的未出現號碼
- **依大小排序**：每期按號碼大小排序的未出現號碼
- **格式**：日期 | 號碼1 | 號碼2 | ... （每10個號碼換列）
- **對齊顯示**：超過10個號碼時自動換列並對齊前一列
- **時間排序**：由舊到新排列
- **命中標示**：與實際開獎號碼相符的號碼後加★標記

#### 分頁4：尾數統計
- **每期分析結果**：顯示每一次分析的尾數統計
- **按統計次數排序**：每期按尾數統計次數由高到低排序
- **格式**：日期 | 尾數1 | 尾數2 | ... （每10個尾數換列）
- **不使用padding zero**：尾數顯示為 1, 2, 3 而非 01, 02, 03
- **時間排序**：由舊到新排列
- **命中標示**：與實際開獎號碼尾數相符的預測尾數後加★標記

#### 分頁5：實際開獎號碼
- **每期開獎結果**：顯示每一期的實際開獎號碼
- **號碼排序**：開獎號碼按大小排序顯示
- **格式**：分析日期 | 預測日期 | 號碼1 | 號碼2 | ... | 特別號（如適用）
- **日期說明**：分析日期為進行分析的日期，預測日期從API響應中獲取
- **預測期數標示**：標題顯示預測幾期後的結果
- **特別號智能顯示**：只有實際有特別號的彩種才顯示特別號欄位
- **未開獎處理**：如果預測期號為空，標記為「尚未開獎」
- **時間排序**：由舊到新排列

### 3. 數據格式優化
✅ **號碼格式**
- 一般號碼：使用 padding zero（01, 02, 03...）
- 尾數號碼：不使用 padding zero（1, 2, 3...）
- 命中號碼：前加 `*` 標記

✅ **欄位結構**
- 每個號碼獨立一欄，便於Excel處理
- 使用合併儲存格創建跨欄標題
- 自動調整欄寬以適應內容

### 4. 詳細統計功能
✅ **數據收集增強**
- 收集每期的詳細分析統計
- 包含預測號碼出現次數
- 包含未出現號碼統計
- 包含尾數統計

✅ **篩選邏輯整合**
- 分析結果會根據設定的篩選條件進行過濾
- 只有符合準確次數和準確率條件的結果會被統計
- 確保Excel匯出的數據品質

## 技術實現細節

### 新增函數
1. `extractDetailedAnalysis()` - 提取詳細分析統計
2. `getMaxNumberForLottoType()` - 獲取彩種最大號碼
3. `createBallFollowMainSheet()` - 創建主要分析結果分頁
4. `createPredictNumbersSheet()` - 創建預測號碼統計分頁
5. `createNonAppearedSheet()` - 創建未出現號碼分頁
6. `createTailNumbersSheet()` - 創建尾數統計分頁
7. `applyAdvancedExcelStyles()` - 應用高級Excel樣式

### 數據結構擴展
```typescript
interface BatchAnalysisResult {
  // 原有欄位...
  targetNumAppearances?: Map<number, number>;     // 預測號碼出現次數
  nonAppearedNumbers?: number[];                  // 未出現號碼
  nonAppearedByFrequency?: Map<number, number>;   // 未出現號碼頻率
  tailNumAppearances?: Map<number, number>;       // 尾數出現次數
}
```

### 篩選邏輯
- 在 `extractDetailedAnalysis()` 中實現篩選
- 根據 `accuracy.value` 和 `accuracyRate.value` 過濾結果
- 確保統計數據的準確性

## 使用說明

### 操作流程
1. 選擇「版路分析」方法
2. **在參數設定區域**設定篩選條件（準確次數、準確率）
3. 配置其他分析參數
4. 執行批量分析
5. 下載Excel報表

### Excel報表內容
- **預測號碼**：每期的預測號碼統計（按出現次數排序，時間由舊到新，號碼後★標示命中）
- **未出現號碼-預測次數**：每期按預測次數排序的未出現號碼（時間由舊到新，號碼後★標示命中）
- **未出現號碼-大小排序**：每期按大小排序的未出現號碼（時間由舊到新，號碼後★標示命中）
- **尾數統計**：每期的尾數分布統計（按統計次數排序，時間由舊到新，尾數後★標示命中）
- **實際開獎號碼**：每期的實際開獎結果（分析日期+預測日期，按大小排序，智能顯示特別號）

### 注意事項
- 篩選條件在版路分析參數設定中顯示
- **命中標示**：與實際開獎號碼相符的預測號碼和尾數後加★符號標示
- **實際開獎號碼**：新增完整的實際開獎號碼分頁，包含特別號資訊
- 尾數不使用 padding zero
- **拆分未出現號碼分頁**：未出現號碼的兩種排序方式各自獨立分頁
- **移除分析結果分頁**：不再產生主要的分析結果對比分頁
- **時間排序調整**：所有分頁的時間排序都改為由舊到新
- **尾數排序優化**：尾數統計按統計次數排序，而非尾數大小排序
- **每期獨立統計**：預測號碼、未出現號碼、尾數統計都是每期的分析結果
- **自動換列對齊**：超過10個號碼時自動換列並對齊前一列

## 最新修改 (第六次優化)

### ✅ 已完成的修改
1. **簡化命中標記**：
   - 將命中標記從 `★號碼★` 改為 `號碼★`
   - 減少視覺干擾，提高可讀性

2. **修復實際開獎號碼分頁**：
   - 修正預測日期獲取邏輯，從API響應中正確獲取
   - 移除錯誤的日期計算函數
   - 根據期號是否存在判斷是否已開獎
   - 未開獎時標記為「尚未開獎」

## 第五次優化回顧

### ✅ 已完成的修改
1. **優化實際開獎號碼分頁**：
   - 添加分析日期和預測日期兩個欄位，避免使用者誤會
   - 在標題顯示預測期數說明
   - 智能顯示特別號欄位（只有實際有特別號的彩種才顯示）
   - 即使未開獎也顯示預測日期，便於與其他分頁對照

## 第四次優化回顧

### ✅ 已完成的修改
1. **修復命中標示功能**：由於XLSX庫限制，改用★符號標記命中號碼
2. **新增實際開獎號碼分頁**：添加完整的實際開獎號碼資訊
3. **特別號支援**：正確處理有特別號的彩種，並在表頭標記

### 📊 最終Excel結構
版路分析現在產生5個分頁：
1. **預測號碼**：每期按出現次數排序的預測號碼（★標示命中號碼）
2. **未出現號碼-預測次數**：每期按預測次數排序的未出現號碼（★標示命中號碼）
3. **未出現號碼-大小排序**：每期按大小排序的未出現號碼（★標示命中號碼）
4. **尾數統計**：每期按統計次數排序的尾數分布（★標示命中尾數）
5. **實際開獎號碼**：每期的實際開獎結果（包含特別號）

## 第三次優化回顧

### ✅ 已完成的修改
1. **拆分未出現號碼分頁**：將未出現號碼的兩種排序方式拆分成兩個獨立分頁
2. **實現命中標示功能**：對與實際開獎號碼相符的預測號碼和尾數進行標示

## 第二次優化回顧

### ✅ 已完成的修改
1. **移除分析結果分頁**：不再產生主要的預測與實際對比分頁
2. **時間排序調整**：將所有分析結果的時間排序改為由舊到新（與原本相反）
3. **尾數統計排序優化**：尾數統計改為按統計次數排序，而非尾數大小排序

### 🔧 技術實現細節

#### 新增函數
1. `createNonAppearedByFrequencySheet()` - 創建依預測次數排序的未出現號碼分頁
2. `createNonAppearedBySizeSheet()` - 創建依大小排序的未出現號碼分頁
3. `createActualNumbersSheet()` - 創建實際開獎號碼分頁
4. `applyRedFontForMatches()` - 應用命中標示給匹配的號碼


#### 命中標示實現
- 由於標準XLSX庫不支援樣式，改用★符號標記命中號碼
- 自動比對預測號碼與實際開獎號碼
- 尾數比對：將實際開獎號碼轉換為尾數後進行比對
- 標記格式：`號碼★` 用於標示命中的號碼或尾數（簡化版本）

#### 實際開獎號碼處理
- 正確獲取包含特別號的完整開獎號碼
- 自動識別彩種並處理特別號邏輯
- 威力彩：所有號碼都是一般號碼
- 其他彩種：最後一個號碼為特別號
- 預測日期從API響應中獲取：使用predictResponse.draw_date
- 開獎狀態判斷：根據period是否存在判斷是否已開獎
- 雙日期顯示：分析日期和預測日期分別顯示，避免混淆

## 後續改進建議

1. **更多篩選條件**：可考慮添加更多篩選選項
2. **圖表功能**：在Excel中添加統計圖表
3. **性能優化**：對大量數據的處理進行優化
4. **條件格式**：可考慮添加更多Excel條件格式功能
