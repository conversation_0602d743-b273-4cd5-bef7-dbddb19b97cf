// 分析參數類型定義
export interface BallFollowParameters {
  comb1: number;        // 第一組號碼數
  comb2: number;        // 第二組號碼數
  comb3: number;        // 目標組號碼數
  periodNum: number;   // 推算期數
  maxRange: number;    // 拖牌區間
  aheadNum: number;    // 預測期數
}

export interface TailParameters {
  tailComb1: number;        // 第一組尾數數
  tailComb2: number;        // 第二組尾數數
  tailComb3: number;        // 目標組尾數數
  periodNum: number;   // 推算期數
  maxRange: number;    // 拖牌區間
  aheadNum: number;    // 預測期數
}

export interface PatternParameters {
  comb1: number;       // 第一組組合數
  comb2: number;       // 第二組組合數
  comb3: number;       // 第三組組合數
  tailComb1: number;   // 第一組尾數組合數
  tailComb2: number;   // 第二組尾數組合數
  tailComb3: number;   // 第三組尾數組合數
  periodNum: number;   // 推算期數
  maxRange: number;    // 拖牌區間
  aheadNum: number;    // 預測期數
}

export type AnalysisParameters = BallFollowParameters | TailParameters | PatternParameters;
