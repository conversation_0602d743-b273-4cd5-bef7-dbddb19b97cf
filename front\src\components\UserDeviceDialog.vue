<template>
  <q-dialog v-model="visible" persistent>
    <q-card class="user-device-dialog">
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">用戶設備管理</div>
        <q-space />
        <q-btn icon="close" flat round dense v-close-popup />
      </q-card-section>

      <q-card-section>
        <div class="text-subtitle2 q-mb-md">
          用戶: {{ userData?.name }} ({{ userData?.uid }})
        </div>

        <!-- 設備列表 -->
        <q-card flat bordered class="q-mb-md">
          <q-card-section>
            <div class="text-h6 q-mb-md">當前設備 ({{ devices.length }}/2)</div>

            <q-table
              :rows="devices"
              :columns="deviceColumns"
              row-key="id"
              flat
              :loading="isLoadingDevices"
              :rows-per-page-options="[0]"
              hide-pagination
            >
              <template v-slot:body="props">
                <q-tr :props="props">
                  <q-td key="device_name" :props="props">
                    <div class="text-weight-medium">{{ props.row.device_name }}</div>
                    <div class="text-caption text-grey-6">{{ props.row.device_id }}</div>
                  </q-td>
                  <q-td key="is_active" :props="props">
                    <q-chip
                      :color="props.row.is_active ? 'positive' : 'grey'"
                      text-color="white"
                      size="md"
                    >
                      {{ props.row.is_active ? '活躍' : '非活躍' }}
                    </q-chip>
                  </q-td>
                  <q-td key="last_seen_at" :props="props">
                    {{ formatDateTimeTW(props.row.last_seen_at) }}
                  </q-td>
                  <q-td key="created_at" :props="props">
                    {{ formatDateTimeTW(props.row.created_at) }}
                  </q-td>
                  <q-td key="actions" :props="props">
                    <q-btn
                      dense
                      outline
                      round
                      icon="delete"
                      color="red"
                      size="md"
                      @click="deleteDevice(props.row)"
                    />
                  </q-td>
                </q-tr>
              </template>

              <template v-slot:no-data>
                <div class="full-width row flex-center text-grey q-gutter-sm">
                  <span>此用戶沒有註冊的設備</span>
                </div>
              </template>
            </q-table>
          </q-card-section>
        </q-card>

        <!-- 登入歷史 -->
        <q-card flat bordered>
          <q-card-section>
            <div class="text-h6 q-mb-md">登入歷史 (最近50次)</div>

            <q-table
              :rows="parsedUserAgents"
              :columns="historyColumns"
              row-key="id"
              flat
              :loading="isLoadingHistory"
              :rows-per-page-options="[10, 20, 50]"
              :pagination="{ rowsPerPage: 10 }"
            >
              <template v-slot:body="props">
                <q-tr :props="props">
                  <q-td key="ip_address" :props="props">
                    {{ props.row.ip_address }}
                  </q-td>
                  <q-td key="user_agent" :props="props">
                    <div class="user-agent-cell">
                      <div class="user-agent-summary">
                        <q-icon
                          :name="getDeviceTypeIcon(props.row.parsedUA.deviceType)"
                          size="sm"
                          class="q-mr-xs"
                          :color="getBrowserIcon(props.row.parsedUA.browser).color"
                        />
                        <span class="text-body2">
                          {{ formatUserAgentShort(props.row.parsedUA) }}
                        </span>
                      </div>
                      <q-tooltip
                        class="bg-dark text-white"
                        anchor="top middle"
                        self="bottom middle"
                        max-width="400px"
                      >
                        <div class="user-agent-tooltip">
                          <div class="text-weight-bold q-mb-sm">設備詳細信息</div>
                          <div class="user-agent-details">
                            {{ formatUserAgentDetailed(props.row.parsedUA) }}
                          </div>
                          <q-separator class="q-my-sm" />
                          <div class="text-caption">
                            <strong>完整 User-Agent:</strong><br>
                            {{ props.row.user_agent }}
                          </div>
                        </div>
                      </q-tooltip>
                    </div>
                  </q-td>
                  <q-td key="is_success" :props="props">
                    <q-chip
                      :color="props.row.is_success ? 'positive' : 'negative'"
                      text-color="white"
                      size="md"
                    >
                      {{ props.row.is_success ? '成功' : '失敗' }}
                    </q-chip>
                  </q-td>
                  <q-td key="created_at" :props="props">
                    {{ formatDateTimeTW(props.row.created_at) }}
                  </q-td>
                </q-tr>
              </template>

              <template v-slot:no-data>
                <div class="full-width row flex-center text-grey q-gutter-sm">
                  <span>沒有登入歷史記錄</span>
                </div>
              </template>
            </q-table>
          </q-card-section>
        </q-card>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { Notify } from 'quasar';
import USER_API, { UserDevice, LoginHistory, UserData } from '@/api/modules/user';
import { useDialog, handleError } from '@/utils';
import { formatDateTimeTW } from '@/utils/date-formatter';
import {
  parseUserAgent,
  formatUserAgentShort,
  formatUserAgentDetailed,
  getDeviceTypeIcon,
  getBrowserIcon
} from '@/utils/user-agent-parser';

interface Props {
  visible: boolean;
  userData: UserData | null;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

// 解析用戶代理信息的計算屬性
const parsedUserAgents = computed(() => {
  return loginHistory.value.map(item => ({
    ...item,
    parsedUA: parseUserAgent(item.user_agent)
  }));
});

const devices = ref<UserDevice[]>([]);
const loginHistory = ref<LoginHistory[]>([]);
const isLoadingDevices = ref(false);
const isLoadingHistory = ref(false);

const deviceColumns = [
  {
    name: 'device_name',
    label: '設備名稱',
    field: 'device_name',
    align: 'left' as const,
  },
  {
    name: 'is_active',
    label: '狀態',
    field: 'is_active',
    align: 'center' as const,
  },
  {
    name: 'last_seen_at',
    label: '最後活動時間',
    field: 'last_seen_at',
    align: 'center' as const,
  },
  {
    name: 'created_at',
    label: '註冊時間',
    field: 'created_at',
    align: 'center' as const,
  },
  {
    name: 'actions',
    label: '操作',
    field: 'actions',
    align: 'center' as const,
  },
];

const historyColumns = [
  {
    name: 'ip_address',
    label: 'IP地址',
    field: 'ip_address',
    align: 'left' as const,
    style: 'width: 120px',
  },
  {
    name: 'user_agent',
    label: '設備信息',
    field: 'user_agent',
    align: 'left' as const,
    style: 'width: 200px; min-width: 200px',
  },
  {
    name: 'is_success',
    label: '結果',
    field: 'is_success',
    align: 'center' as const,
    style: 'width: 80px',
  },
  {
    name: 'created_at',
    label: '時間',
    field: 'created_at',
    align: 'center' as const,
    style: 'width: 140px',
  },
];

const loadDevices = async () => {
  if (!props.userData?.id) return;

  try {
    isLoadingDevices.value = true;
    const response = await USER_API.getUserDevices(props.userData.id);
    devices.value = response.data.devices;
  } catch (error) {
    handleError(error);
  } finally {
    isLoadingDevices.value = false;
  }
};

const loadLoginHistory = async () => {
  if (!props.userData?.id) return;

  try {
    isLoadingHistory.value = true;
    const response = await USER_API.getUserLoginHistory(props.userData.id);
    loginHistory.value = response.data.login_history;
  } catch (error) {
    handleError(error);
  } finally {
    isLoadingHistory.value = false;
  }
};

const deleteDevice = (device: UserDevice) => {
  useDialog().showMessage({
    title: '確定要刪除此設備嗎？',
    message: `設備: ${device.device_name}`,
    timeout: 0,
    ok: async () => {
      try {
        await USER_API.deleteUserDevice(props.userData?.id || '', device.device_id);
        Notify.create({
          message: '設備已刪除',
          color: 'positive',
          timeout: 2000,
          position: 'top',
        });
        loadDevices(); // 重新載入設備列表
      } catch (error) {
        handleError(error);
      }
    },
  });
};

// 監聽對話框開啟，載入數據
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible && props.userData) {
      loadDevices();
      loadLoginHistory();
    }
  }
);
</script>

<style lang="scss" scoped>
// 對話框響應式設計
.user-device-dialog {
  min-width: 900px;
  max-width: 95vw;
  width: 100%;

  .q-card {
    max-width: 100%;
    width: 100%;
  }

  @media (max-width: 1024px) {
    min-width: 800px;
  }

  @media (max-width: 768px) {
    min-width: 95vw;
    max-width: 95vw;
  }
}

.user-agent-cell {
  max-width: 200px;

  .user-agent-summary {
    display: flex;
    align-items: center;
    cursor: help;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s;

    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }

    span {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.user-agent-tooltip {
  max-width: 400px;

  .user-agent-details {
    white-space: pre-line;
    line-height: 1.4;
    margin-bottom: 8px;
  }

  .text-caption {
    word-break: break-all;
    line-height: 1.3;
    opacity: 0.8;
  }
}

// 表格樣式優化
:deep(.q-table) {
  .q-td {
    vertical-align: top;

    &[data-col="user_agent"] {
      padding: 8px 12px;
    }
  }

  .q-table__container {
    max-height: 400px;
  }
}

// 對話框樣式
.q-card {
  .q-card-section {
    &:last-child {
      padding-bottom: 16px;
    }
  }
}
</style>
