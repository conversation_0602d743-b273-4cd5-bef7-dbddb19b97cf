# 安全性改進文檔

## 問題描述

原系統存在重要的安全漏洞：前端只檢查本地存儲的token而不驗證其在後端的有效性，導致已被撤銷的token仍然可以訪問受保護的資源。

## 主要問題

1. **路由守衛只檢查本地token**：`isAuthenticated` 只檢查 `!!state.accessToken`
2. **沒有驗證token在後端的有效性**：token可能已被撤銷但前端不知道
3. **管理員權限檢查也只基於本地數據**：可能基於過期的用戶信息

## 解決方案

### 1. 新增token驗證方法

在 `auth store` 中添加了以下新方法：

- `validateToken()`: 向後端驗證token是否仍然有效
- `checkAuthentication()`: 完整的認證檢查（包含後端驗證）
- `hasLocalToken`: 新的getter，只檢查本地是否有token（不驗證有效性）

### 2. 修改路由守衛

- 將 `authStore.isAuthenticated` 替換為 `await authStore.checkAuthentication()`
- 管理員權限檢查現在會重新驗證用戶資料

### 3. 定期token驗證

- 每5分鐘自動驗證一次token有效性
- 如果發現token失效，自動登出用戶

### 4. 改進的WebSocket連接管理

- 使用 `hasLocalToken` 而不是 `isAuthenticated` 來避免不必要的後端請求
- 在適當的時機進行完整的認證檢查

## 安全機制詳解

### Token驗證流程

```
1. 檢查本地是否有token
   ↓
2. 檢查token是否過期
   ↓ (如果過期)
3. 嘗試刷新token
   ↓ (如果刷新失敗或refresh token過期)
4. 向後端驗證token有效性
   ↓ (如果無效)
5. 自動登出並跳轉到登入頁面
```

### 路由保護

- **需要認證的路由**：使用 `checkAuthentication()` 進行完整驗證
- **管理員路由**：額外驗證管理員權限，確保用戶資料是最新的

### 定期驗證

- 每5分鐘檢查一次token有效性
- 在用戶活躍時進行，避免不必要的網絡請求
- 發現token失效時立即登出

## 修改的文件

1. `front/src/stores/auth.ts`
   - 新增 `validateToken()` 方法
   - 新增 `checkAuthentication()` 方法
   - 新增定期驗證機制
   - 修改WebSocket相關的認證檢查

2. `front/src/router/index.ts`
   - 修改路由守衛使用新的認證檢查
   - 改進管理員權限驗證

3. `front/src/pages/WebSocketTestPage.vue`
   - 使用新的認證檢查方法

4. `front/src/pages/LoginPage.vue`
   - 改進登入後的權限檢查

## 向後兼容性

- 保留了原有的 `isAuthenticated` getter，但建議在需要安全驗證的地方使用新方法
- 新增的 `hasLocalToken` getter 可用於不需要後端驗證的場景（如WebSocket重連）

## 性能考慮

- 定期驗證間隔設為5分鐘，平衡安全性和性能
- 路由守衛中的後端驗證會增加頁面載入時間，但提高了安全性
- 使用快取機制避免重複驗證

## 建議的使用方式

### 何時使用 `isAuthenticated`
- 純UI顯示邏輯（如顯示/隱藏登入按鈕）
- 不涉及安全性的功能

### 何時使用 `checkAuthentication()`
- 路由守衛
- 重要操作前的驗證
- 敏感頁面的訪問控制

### 何時使用 `hasLocalToken`
- WebSocket連接管理
- 不需要後端驗證的本地檢查

## 測試建議

1. **Token撤銷測試**：
   - 在一個瀏覽器登入
   - 在另一個瀏覽器用同一帳號登入（觸發token撤銷）
   - 驗證第一個瀏覽器是否被正確登出

2. **定期驗證測試**：
   - 登入後等待5分鐘以上
   - 檢查是否有定期驗證的網絡請求

3. **路由保護測試**：
   - 手動修改localStorage中的token
   - 嘗試訪問受保護的路由
   - 驗證是否被正確重定向到登入頁面

## 未來改進

1. 可考慮實現更智能的驗證策略（如基於用戶活動）
2. 添加token即將過期的提醒機制
3. 實現更細粒度的權限控制
