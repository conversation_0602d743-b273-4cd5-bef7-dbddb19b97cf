// 版本檢查服務 - 專注於版本檢測邏輯，UI交互由App.vue統一處理
export class VersionChecker {
  private static instance: VersionChecker;
  private currentVersion: string;
  private checkInterval: number | null = null;
  private buildVersion: string;

  private constructor() {
    // 從環境變數獲取構建版本（包含時間戳）
    this.buildVersion = process.env.APP_VERSION || 'unknown';
    // 提取基礎版本號（去掉時間戳）
    this.currentVersion = this.extractBaseVersion(this.buildVersion);
  }

  private extractBaseVersion(fullVersion: string): string {
    // 從 "v1.2.4_123456" 格式中提取 "v1.2.4"
    const match = fullVersion.match(/^(v\d+\.\d+\.\d+)/);
    return match ? match[1] : fullVersion;
  }

  public static getInstance(): VersionChecker {
    if (!VersionChecker.instance) {
      VersionChecker.instance = new VersionChecker();
    }
    return VersionChecker.instance;
  }

  public startVersionCheck() {
    // 在開發環境中完全禁用版本檢查
    if (process.env.DEV) {
      return;
    }

    // 生產環境每60秒檢查一次版本
    this.checkInterval = window.setInterval(() => {
      this.checkVersion();
    }, 60000);

    // 延遲10秒後開始第一次檢查
    setTimeout(() => this.checkVersion(), 10000);
  }

  private async checkVersion() {
    // 在開發環境中不執行版本檢查
    if (process.env.DEV) {
      return;
    }

    try {

      // 方法1: 檢查 index.html 的 ETag/Last-Modified
      const indexResponse = await fetch('/index.html?' + Date.now(), {
        method: 'HEAD',
        cache: 'no-cache'
      });

      if (indexResponse.ok) {
        const etag = indexResponse.headers.get('etag');
        const lastModified = indexResponse.headers.get('last-modified');

        const storedEtag = localStorage.getItem('app-etag');
        const storedLastModified = localStorage.getItem('app-last-modified');

        // 檢查是否為第一次訪問（沒有儲存的值）
        const isFirstVisit = !storedEtag && !storedLastModified;

        // 檢查是否為第一次會話（避免無痕模式和首次訪問時的誤報）
        const isFirstSession = !sessionStorage.getItem('app-session-started');

        if (!isFirstVisit && !isFirstSession) {
          // 不是第一次訪問且不是第一次會話，檢查是否有變更
          if (etag && storedEtag && etag !== storedEtag) {
            // VersionChecker: 檢測到新版本 (ETag 變更)
            this.handleNewVersion();
            return;
          }

          if (lastModified && storedLastModified && lastModified !== storedLastModified) {
            // VersionChecker: 檢測到新版本 (Last-Modified 變更)
            this.handleNewVersion();
            return;
          }
        }

        // 儲存當前的標頭值（第一次訪問或沒有變更時）
        if (etag) localStorage.setItem('app-etag', etag);
        if (lastModified) localStorage.setItem('app-last-modified', lastModified);
      }

      // 方法2: 檢查版本文件（如果存在）
      await this.checkVersionFile();

    } catch (error) {
      console.error('VersionChecker: 版本檢查失敗', error);
    }
  }

  private async checkVersionFile() {
    // 在開發環境中不執行版本文件檢查
    if (process.env.DEV) {
      return;
    }

    try {
      // 嘗試獲取版本信息文件
      const response = await fetch('/version.json?' + Date.now(), {
        cache: 'no-cache'
      });

      if (response.ok) {
        const versionData = await response.json();
        const serverVersion = versionData.version;

        // 檢查是否為第一次訪問（沒有儲存的版本信息）
        const storedVersion = localStorage.getItem('app-stored-version');
        const isFirstVisit = !storedVersion;

        // 檢查是否為第一次會話（避免無痕模式和首次訪問時的誤報）
        const isFirstSession = !sessionStorage.getItem('app-session-started');

        if (!isFirstVisit && !isFirstSession && serverVersion && serverVersion !== this.buildVersion) {
          // 不是第一次訪問、不是第一次會話且檢測到新版本
          this.handleNewVersion();
        }

        // 儲存當前版本信息（第一次訪問或沒有變更時）
        if (serverVersion) {
          localStorage.setItem('app-stored-version', serverVersion);
        }
      }
    } catch (error) {
      // 版本文件不存在或無法讀取，這是正常的
      console.log('VersionChecker: 版本文件檢查失敗（可能不存在）', error instanceof Error ? error.message : String(error));
    }
  }

  // 發現新版本，通知App.vue處理
  private handleNewVersion() {
    // 發送自定義事件通知App.vue，讓App.vue統一處理更新邏輯
    const event = new CustomEvent('version-update-detected', {
      detail: {
        currentVersion: this.buildVersion,
        isDev: process.env.DEV
      }
    });
    window.dispatchEvent(event);

    // 在開發環境中直接刷新
    if (process.env.DEV) {
      this.performDirectUpdate();
    }
    // 生產環境由App.vue統一處理更新邏輯
  }



  // 執行更新（由App.vue調用）
  public async performUpdate(): Promise<void> {
    console.log('VersionChecker: 開始執行更新');

    try {
      // 清除所有緩存
      await this.clearAllCaches();

      // 重新載入頁面
      window.location.reload();
    } catch (error) {
      console.error('VersionChecker: 更新失敗', error);
      throw error; // 讓調用方處理錯誤
    }
  }

  private async performDirectUpdate() {
    console.log('VersionChecker: 開發環境直接更新');

    try {
      // 清除所有緩存
      await this.clearAllCaches();

      // 直接重新載入頁面，不顯示進度
      window.location.reload();
    } catch (error) {
      console.error('VersionChecker: 直接更新失敗', error);
      // 即使清除緩存失敗，也嘗試刷新頁面
      window.location.reload();
    }
  }



  private async clearAllCaches() {
    try {
      // 清除 Service Worker 緩存
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(
          cacheNames.map(cacheName => caches.delete(cacheName))
        );
        console.log('VersionChecker: 已清除所有緩存');
      }

      // 清除 localStorage 中的版本相關資訊
      localStorage.removeItem('app-etag');
      localStorage.removeItem('app-last-modified');
      localStorage.removeItem('app-stored-version');
    } catch (error) {
      console.error('VersionChecker: 清除緩存失敗', error);
    }
  }

  public destroy() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
  }

  public getCurrentVersion(): string {
    return this.currentVersion;
  }

  public getBuildVersion(): string {
    return this.buildVersion;
  }

  public getVersionInfo() {
    return {
      baseVersion: this.currentVersion,
      buildVersion: this.buildVersion,
      timestamp: this.extractTimestamp(this.buildVersion)
    };
  }

  private extractTimestamp(fullVersion: string): string {
    // 從 "v1.2.4_123456" 格式中提取時間戳
    const match = fullVersion.match(/_(\d+)$/);
    return match ? match[1] : '';
  }

  // 公共更新方法（由App.vue調用）
  public async triggerUpdate(): Promise<void> {
    return this.performUpdate();
  }

  // 手動檢查版本
  public async manualVersionCheck(): Promise<boolean> {
    // 在開發環境中不執行手動版本檢查
    if (process.env.DEV) {
      return false;
    }

    // 檢查是否為第一次會話（避免無痕模式和首次訪問時的誤報）
    const isFirstSession = !sessionStorage.getItem('app-session-started');
    if (isFirstSession) {
      console.log('VersionChecker: 第一次會話，跳過手動版本檢查');
      return false;
    }

    try {
      // 檢查 index.html 的變更
      const indexChanged = await this.checkIndexChanges();
      if (indexChanged) {
        return true;
      }

      // 檢查版本文件
      const versionChanged = await this.checkVersionFileChanges();
      if (versionChanged) {
        return true;
      }

      return false;
    } catch (error) {
      console.error('VersionChecker: 手動版本檢查失敗', error);
      throw error;
    }
  }

  private async checkIndexChanges(): Promise<boolean> {
    const response = await fetch('/index.html?' + Date.now(), {
      method: 'HEAD',
      cache: 'no-cache'
    });

    if (!response.ok) {
      throw new Error('無法獲取 index.html');
    }

    const etag = response.headers.get('etag');
    const lastModified = response.headers.get('last-modified');

    const storedEtag = localStorage.getItem('app-etag');
    const storedLastModified = localStorage.getItem('app-last-modified');

    // 如果沒有儲存的值，表示是第一次訪問，不應該觸發更新
    if (!storedEtag && !storedLastModified) {
      return false;
    }

    if (etag && storedEtag && etag !== storedEtag) {
      return true;
    }

    if (lastModified && storedLastModified && lastModified !== storedLastModified) {
      return true;
    }

    return false;
  }

  private async checkVersionFileChanges(): Promise<boolean> {
    // 在開發環境中跳過版本文件檢查
    if (process.env.DEV) {
      return false;
    }

    try {
      const response = await fetch('/version.json?' + Date.now(), {
        cache: 'no-cache'
      });

      if (response.ok) {
        const versionData = await response.json();
        const serverVersion = versionData.version;

        // 檢查是否有儲存的版本信息，如果沒有表示是第一次訪問
        const storedVersion = localStorage.getItem('app-stored-version');
        if (!storedVersion) {
          return false;
        }

        if (serverVersion && serverVersion !== this.buildVersion) {
          // 服務器版本與本地版本不同
          return true;
        }
      }
      return false;
    } catch (error) {
      // 版本文件不存在是正常的
      console.log('VersionChecker: 版本文件不存在或無法訪問');
      return false;
    }
  }
}

// 創建全局實例
export const versionChecker = VersionChecker.getInstance();
