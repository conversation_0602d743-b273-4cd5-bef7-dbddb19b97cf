interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

export interface SafariNavigator extends Navigator {
  standalone?: boolean;
}

declare global {
  interface Window {
    savedBeforeInstallPromptEvent?: BeforeInstallPromptEvent;
  }
}

class PWAInstallService {
  private static instance: PWAInstallService;
  private initialized = false;
  private deferredInstallPrompt?: BeforeInstallPromptEvent;
  private installPromptListeners: Set<(e: BeforeInstallPromptEvent) => void> =
    new Set();

  private constructor() {
    this.setupEventListener();
  }

  private setupEventListener() {
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      const promptEvent = e as BeforeInstallPromptEvent;
      this.deferredInstallPrompt = promptEvent;
      window.savedBeforeInstallPromptEvent = promptEvent;

      // 通知所有監聽器
      this.installPromptListeners.forEach((listener) => listener(promptEvent));
    };

    // 立即開始監聽事件
    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
  }

  public static getInstance(): PWAInstallService {
    if (!PWAInstallService.instance) {
      PWAInstallService.instance = new PWAInstallService();
    }
    return PWAInstallService.instance;
  }

  public checkInstallability(): {
    canInstall: boolean;
    message: string;
    isStandalone: boolean;
    isIOS: boolean;
  } {
    // 檢查是否已經以 PWA 方式安裝
    const isStandalone =
      window.matchMedia('(display-mode: standalone)').matches ||
      (window.navigator as SafariNavigator).standalone ||
      document.referrer.includes('android-app://');

    if (isStandalone) {
      return {
        canInstall: false,
        message: '應用程式已經安裝',
        isStandalone: true,
        isIOS: this.isIOS(),
      };
    }

    // 檢查是否為 HTTPS 環境
    const isHttps =
      window.location.protocol === 'https:' ||
      window.location.hostname === 'localhost' ||
      window.location.hostname === '***********';

    if (!isHttps) {
      return {
        canInstall: false,
        message: '安裝 PWA 需要 HTTPS 環境',
        isStandalone: false,
        isIOS: this.isIOS(),
      };
    }

    // 檢查是否為 iOS 裝置
    if (this.isIOS()) {
      return {
        canInstall: true,
        message: '請使用 Safari 的分享選單，選擇「加入主畫面」',
        isStandalone: false,
        isIOS: this.isIOS(),
      };
    }

    // 檢查瀏覽器支援
    const isSupportedPlatform = 'serviceWorker' in navigator;
    if (!isSupportedPlatform) {
      return {
        canInstall: false,
        message: '您的瀏覽器不支援 PWA 安裝',
        isStandalone: false,
        isIOS: this.isIOS(),
      };
    }

    return {
      canInstall: true,
      message: window.savedBeforeInstallPromptEvent
        ? '您可以安裝此應用到您的裝置上'
        : '請使用 Chrome 瀏覽器訪問此頁面以安裝應用',
      isStandalone: false,
      isIOS: this.isIOS(),
    };
  }

  public isIOS(): boolean {
    return /iPhone|iPad|iPod/.test(navigator.userAgent);
  }

  public initialize() {
    if (this.initialized) {
      return;
    }

    this.initialized = true;
  }

  // 新增訂閱方法
  public onBeforeInstallPrompt(
    callback: (e: BeforeInstallPromptEvent) => void
  ) {
    this.installPromptListeners.add(callback);

    // 如果已經有暫存的事件，立即觸發
    if (this.deferredInstallPrompt) {
      callback(this.deferredInstallPrompt);
    }

    // 返回取消訂閱的函數
    return () => {
      this.installPromptListeners.delete(callback);
    };
  }

  // 檢查是否有可用的安裝提示
  public hasInstallPrompt(): boolean {
    return (
      !!this.deferredInstallPrompt || !!window.savedBeforeInstallPromptEvent
    );
  }
}

export const pwaInstallService = PWAInstallService.getInstance();
