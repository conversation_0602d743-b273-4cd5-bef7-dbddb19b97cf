<template>
  <q-page class="justify-center">
    <q-card class="q-mx-auto q-py-lg q-px-md" style="max-width: 600px">
      <q-card-section>
        <div class="text-h5 text-center text-weight-bold q-mb-lg">
          個人資料
        </div>
      </q-card-section>

      <!-- 基本資料顯示 -->
      <q-card-section class="q-gutter-md">
        <div class="text-h6 text-weight-bold">基本資料</div>

        <q-skeleton v-if="loading" type="rect" height="200px" />

        <div v-else class="q-gutter-md">
          <q-input
            v-model="userProfile.uid"
            label="帳號"
            outlined
            readonly
            class="bg-grey-1"
          />

          <q-input
            v-model="profileForm.name"
            label="姓名"
            outlined
            :rules="[(val) => !!val || '請輸入姓名']"
          />

          <q-input
            v-model="profileForm.email"
            label="電子郵件"
            outlined
            type="email"
          />

          <div class="row">
            <q-input
              v-model="userProfile.created_at"
              label="註冊時間"
              outlined
              readonly
              class="bg-grey-1 col q-mr-sm"
            />

            <q-input
              v-model="userProfile.last_login_at"
              label="最後登入時間"
              outlined
              readonly
              class="bg-grey-1 col"
            />
          </div>

          <q-input
            v-model="userProfile.expires_at"
            label="帳號到期時間"
            outlined
            readonly
            class="bg-grey-1"
          />

          <div class="row justify-end q-gutter-sm">
            <q-btn
              color="primary"
              label="更新資料"
              :loading="updating"
              @click="updateProfile"
            />
          </div>
        </div>
      </q-card-section>

      <q-separator class="q-my-lg" />

      <!-- 更改密碼 -->
      <q-card-section class="q-gutter-md">
        <div class="text-h6 text-weight-bold">更改密碼</div>

        <q-form @submit="changePassword" class="q-gutter-md">
          <q-input
            v-model="passwordForm.new_password"
            label="新密碼"
            type="password"
            outlined
            :rules="[
              (val) => !!val || '請輸入新密碼',
              (val) => val.length >= 4 || '密碼長度需大於 4'
            ]"
          />

          <q-input
            v-model="confirmPassword"
            label="確認新密碼"
            type="password"
            outlined
            :rules="[
              (val) => !!val || '請確認新密碼',
              (val) => val === passwordForm.new_password || '密碼不一致'
            ]"
          />

          <div class="row justify-end q-gutter-sm">
            <q-btn
              color="negative"
              label="重設"
              flat
              @click="resetPasswordForm"
            />
            <q-btn
              color="primary"
              label="更改密碼"
              type="submit"
              :loading="changingPassword"
            />
          </div>
        </q-form>
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Notify } from 'quasar';
import AUTH_API, { UserProfile, UpdateProfileData, ChangePasswordData } from '@/api/modules/auth';
import { useAuthStore } from '@/stores/auth';
// import { useTitleStore } from '@/stores/title';
import { handleError } from '@/utils';
import { useVersionCheck } from '@/composables/useVersionCheck';

defineOptions({
  name: 'ProfilePage',
});

const authStore = useAuthStore();
// const titleStore = useTitleStore();

// 使用版本檢查
useVersionCheck();

// 響應式數據
const loading = ref(false);
const updating = ref(false);
const changingPassword = ref(false);

const userProfile = ref<UserProfile>({
  id: 0,
  uid: '',
  name: '',
  email: '',
  is_active: false,
  expires_at: null,
  last_login_at: null,
  created_at: '',
});

const profileForm = ref<UpdateProfileData>({
  name: '',
  email: '',
});

const passwordForm = ref<ChangePasswordData>({
  new_password: '',
});

const confirmPassword = ref('');

// 格式化日期顯示
const formatDateTime = (dateString: string | null) => {
  if (!dateString) return '無';
  return new Date(dateString).toLocaleString('zh-TW');
};

// 獲取用戶資料
const getUserProfile = async () => {
  try {
    loading.value = true;
    const response = await AUTH_API.getUserProfile();
    userProfile.value = response.data;

    // 格式化日期顯示
    userProfile.value.created_at = formatDateTime(response.data.created_at);
    userProfile.value.last_login_at = formatDateTime(response.data.last_login_at);
    userProfile.value.expires_at = formatDateTime(response.data.expires_at);

    // 初始化表單數據
    profileForm.value = {
      name: response.data.name,
      email: response.data.email,
    };
  } catch (error) {
    handleError(error);
  } finally {
    loading.value = false;
  }
};

// 更新個人資料
const updateProfile = async () => {
  try {
    updating.value = true;
    await AUTH_API.updateUserProfile(profileForm.value);

    // 更新 authStore 中的用戶名稱
    if (authStore.user) {
      authStore.user.name = profileForm.value.name;
    }

    Notify.create({
      message: '個人資料更新成功',
      color: 'positive',
      timeout: 2000,
      position: 'top',
    });

    // 重新獲取資料
    await getUserProfile();
  } catch (error) {
    handleError(error);
  } finally {
    updating.value = false;
  }
};

// 更改密碼
const changePassword = async () => {
  try {
    changingPassword.value = true;
    await AUTH_API.changePassword(passwordForm.value);

    Notify.create({
      message: '密碼更新成功',
      color: 'positive',
      timeout: 3000,
      position: 'top',
    });

    // 清空表單
    resetPasswordForm();
  } catch (error) {
    handleError(error);
  } finally {
    changingPassword.value = false;
  }
};

// 重設密碼表單
const resetPasswordForm = () => {
  passwordForm.value = {
    new_password: '',
  };
  confirmPassword.value = '';
};

// 組件掛載時獲取用戶資料
onMounted(() => {
  // titleStore.val = '個人資料';
  getUserProfile();
});
</script>

<style lang="scss" scoped>
.q-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.bg-grey-1 {
  background-color: #f5f5f5;
}
</style>
