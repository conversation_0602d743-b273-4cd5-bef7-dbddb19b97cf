package controllers

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"gopkg.in/guregu/null.v4"

	. "lottery/database"
	. "lottery/models"
	. "lottery/utils"
)

func GetUserList(c *gin.Context) {
	var data []UserData
	var page Pagination

	if err := c.Should<PERSON>ind<PERSON>uery(&page); err != nil {
		errMsg := ErrorMsg{
			Error: err.Error(),
			Msg:   "Invalid request",
		}
		ErrorLog(errMsg)
		c.JSON(http.StatusBadRequest, errMsg)
		return
	}

	db := ConnectDB()
	defer CloseDB(db)

	tx := db.Model(&User{}).Where("is_admin = FALSE")

	if page.Filter != "" {
		tx = tx.Where("uid LIKE ?", fmt.Sprintf("%s%%", page.Filter)).
			Or("name LIKE ?", fmt.Sprintf("%%%s%%", page.Filter))
	}

	tx.Count(&page.Props.RowsNumber)

	if page.Props.GetLimit() > 0 {
		tx = tx.Limit(page.Props.GetLimit())
	}

	if err := tx.Offset(page.Props.GetOffset()).
		Order(page.Props.GetOrderBy()).
		Find(&data).Error; err != nil {
		errMsg := ErrorMsg{
			Error: err.Error(),
			Msg:   "Failed to get user list",
		}
		ErrorLog(errMsg)
		c.JSON(http.StatusBadRequest, errMsg)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":      data,
		"pageProps": page.Props,
	})
}

func UpdateUserByAdmin(c *gin.Context) {
	req := UserData{}

	if err := c.ShouldBind(&req); err != nil {
		errMsg := ErrorMsg{
			Error: err.Error(),
			Msg:   "Invalid request",
		}
		ErrorLog(errMsg)
		c.JSON(http.StatusBadRequest, errMsg)
		return
	}

	db := ConnectDB()
	defer CloseDB(db)

	if err := db.Model(&User{}).Where("id = ?", req.ID).
		Updates(&req).Error; err != nil {
		errMsg := ErrorMsg{
			Error: err.Error(),
			Msg:   "Failed to update user",
		}
		ErrorLog(errMsg)
		c.JSON(http.StatusBadRequest, errMsg)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": UPDATE_SUCCESS,
	})
}

func UpdateUserActive(c *gin.Context) {
	id := c.Param("id")
	var req struct {
		IsActive bool `json:"is_active"`
	}

	if err := c.ShouldBind(&req); err != nil {
		errMsg := ErrorMsg{
			Error: err.Error(),
			Msg:   "Invalid request",
		}
		ErrorLog(errMsg)
		c.JSON(http.StatusBadRequest, errMsg)
		return
	}

	db := ConnectDB()
	defer CloseDB(db)

	if err := db.Model(&User{}).Where("id = ?", id).Update("is_active", req.IsActive).Error; err != nil {
		errMsg := ErrorMsg{
			Error: err.Error(),
			Msg:   UPDATE_FAILED,
		}
		ErrorLog(errMsg)
		c.JSON(http.StatusBadRequest, errMsg)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": UPDATE_SUCCESS,
	})
}

func UpdateUserExpire(c *gin.Context) {
	id := c.Param("id")
	var req struct {
		ExpiresAt null.String `json:"expires_at"`
	}

	if err := c.ShouldBind(&req); err != nil {
		errMsg := ErrorMsg{
			Error: err.Error(),
			Msg:   "Invalid request",
		}
		ErrorLog(errMsg)
		c.JSON(http.StatusBadRequest, errMsg)
		return
	}

	db := ConnectDB()
	defer CloseDB(db)

	if err := db.Model(&User{}).Where("id = ?", id).Update("expires_at", req.ExpiresAt).Error; err != nil {
		errMsg := ErrorMsg{
			Error: err.Error(),
			Msg:   UPDATE_FAILED,
		}
		ErrorLog(errMsg)
		c.JSON(http.StatusBadRequest, errMsg)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": UPDATE_SUCCESS,
	})
}

func DeleteUser(c *gin.Context) {
	id := c.Param("id")

	db := ConnectDB()
	defer CloseDB(db)

	if err := db.Where("id = ?", id).Delete(&User{}).Error; err != nil {
		errMsg := ErrorMsg{
			Error: err.Error(),
			Msg:   DELETE_FAILED,
		}
		ErrorLog(errMsg)
		c.JSON(http.StatusBadRequest, errMsg)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": DELETE_SUCCESS,
	})
}
