package cmd

import (
	"fmt"
	"log"
	"lottery/models"
	"os"
	"time"

	"github.com/brianvoe/gofakeit/v7"
	"github.com/spf13/cobra"

	"lottery/database"
)

var (
	count   int
	rootCmd = &cobra.Command{
		Use:   "Lotto",
		Short: "Application CLI",
	}
	seedCmd = &cobra.Command{
		Use:   "user-seed",
		Short: "Seed database with test data",
		Run: func(cmd *cobra.Command, args []string) {
			db := database.ConnectDB() // 你的資料庫連線函數

			for i := 0; i < count; i++ {
				user := models.User{
					UID:       gofakeit.Username(),
					Name:      gofakeit.Name(),
					Email:     gofakeit.Email(),
					Pwd:       gofakeit.Password(true, true, true, true, false, 12),
					CreatedAt: gofakeit.DateRange(time.Now().AddDate(0, 0, -30), time.Now()),
				}

				if err := db.Create(&user).Error; err != nil {
					log.Fatal(err)
				}
			}

			fmt.Printf("Successfully generated %d test users\n", count)
		},
	}
)

func init() {
	seedCmd.Flags().IntVarP(&count, "count", "c", 100, "Number of test users to generate")
	rootCmd.AddCommand(seedCmd)
}

func Execute() {
	if err := rootCmd.Execute(); err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
}
