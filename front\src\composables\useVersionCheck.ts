import { onMounted } from 'vue';
import { versionChecker } from '@/services/versionChecker';

/**
 * 組件版本檢查 composable
 * 在組件掛載時檢查是否有新版本
 */
export function useVersionCheck() {
  onMounted(async () => {
    // 在開發環境中跳過
    if (process.env.DEV) {
      return;
    }

    // 檢查是否為第一次載入
    const isFirstLoad = !sessionStorage.getItem('app-session-started');
    if (isFirstLoad) {
      return;
    }

    try {
      const hasUpdate = await versionChecker.manualVersionCheck();
      if (hasUpdate) {
        // 發送版本更新事件，由 App.vue 統一處理
        const event = new CustomEvent('version-update-detected', {
          detail: {
            source: 'component-mount',
            timestamp: Date.now()
          }
        });
        window.dispatchEvent(event);
      }
    } catch (error) {
      console.error('組件版本檢查失敗:', error);
    }
  });
}
