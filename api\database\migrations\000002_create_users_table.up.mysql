CREATE TABLE IF NOT EXISTS `users` (
    `id` BIGINT UNSIGNED NOT NULL PRIMARY KEY,
    `uid` VARCHAR(50) NOT NULL UNIQUE,
    `pwd` VARCHAR(60) NOT NULL,
    `name` VA<PERSON>HA<PERSON>(50) NOT NULL,
    `phone` VARCHAR(20) NOT NULL,
    `email` VARCHAR(50) NOT NULL,
    `expires_at` DATETIME NULL DEFAULT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
    `last_login_at` DATETIME NULL DEFAULT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted_at` DATETIME NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 創建登入記錄表
CREATE TABLE IF NOT EXISTS `login_logs` (
    `id` BIGINT UNSIGNED NOT NULL PRIMARY KEY,
    `user_id` BIGINT UNSIGNED NOT NULL,
    `ip_address` VARCHAR(50) NOT NULL,
    `user_agent` VARCHAR(255) NOT NULL,
    `is_success` BOOLEAN NOT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    KEY `login_logs_user_id_index` (`user_id`),
    CONSTRAINT `login_logs_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 創建訪問令牌表
CREATE TABLE IF NOT EXISTS `access_tokens` (
    `id` BIGINT UNSIGNED NOT NULL PRIMARY KEY,
    `user_id` BIGINT UNSIGNED NOT NULL,
    `token_id` VARCHAR(100) NOT NULL COMMENT 'JWT ID (jti)',
    `access_token` TEXT NULL COMMENT '可選存儲完整token',
    `user_agent` VARCHAR(255) NOT NULL,
    `ip_address` VARCHAR(45) NOT NULL,
    `last_used_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `expires_at` DATETIME NOT NULL,
    `is_revoked` BOOLEAN NOT NULL DEFAULT FALSE,
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `access_tokens_token_id_unique` (`token_id`),
    KEY `access_tokens_user_id_index` (`user_id`),
    KEY `access_tokens_is_revoked_expires_at_index` (`is_revoked`, `expires_at`),
    CONSTRAINT `access_tokens_user_id_foreign` FOREIGN KEY (`user_id`) 
        REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 創建刷新令牌表（如果需要單獨管理）
CREATE TABLE IF NOT EXISTS `refresh_tokens` (
    `id` BIGINT UNSIGNED NOT NULL PRIMARY KEY,
    `access_token_id` BIGINT UNSIGNED NOT NULL,
    `refresh_token` TEXT NOT NULL,
    `expires_at` DATETIME NOT NULL,
    `is_revoked` BOOLEAN NOT NULL DEFAULT FALSE,
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    KEY `refresh_tokens_access_token_id_index` (`access_token_id`),
    CONSTRAINT `refresh_tokens_access_token_id_foreign` FOREIGN KEY (`access_token_id`) 
        REFERENCES `access_tokens` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;