import { defineStore } from 'pinia';
import { LottoItem } from 'src/api/modules/lotto';

export const useLottoStore = defineStore('lotto', {
  state: () => {
    return {
      drawType: '' as string,
      lotto: null as LottoItem | null,
      drawLabel: (drawType: string) => {
        switch (drawType) {
          case 'super_lotto638':
            return '威力彩';
          case 'lotto649':
            return '大樂透';
          case 'daily539':
            return '今彩539';
          case 'lotto_hk':
            return '六合彩';
          case 'ca_lotto':
            return '加州天天樂';
          default:
            return '';
        }
      },
    };
  },
  persist: true,
  getters: {
    getDrawType: (state) => state.drawType,
    getDrawLabel: (state) => state.drawLabel(state.drawType),
    getLotto: (state) => state.lotto,
    getMaxNumber: (state) => {
      switch (state.drawType) {
        case 'super_lotto638':
          return 38;
        case 'lotto649':
          return 49;
        case 'daily539':
          return 39;
        case 'lotto_hk':
          return 49;
        case 'ca_lotto':
          return 39;
        default:
          return 49;
      }
    },
    isSuperLotto: (state) => state.drawType === 'super_lotto638',
  },
  actions: {
    setDrawType(drawType: string) {
      this.drawType = drawType;
    },
    setLotto(lotto: LottoItem) {
      this.lotto = lotto;
    },
  },
});
