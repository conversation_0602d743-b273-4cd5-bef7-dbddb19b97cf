const fs = require('fs');
const path = require('path');
const VersionUtils = require('./version-utils');

// 創建版本工具實例
const versionUtils = new VersionUtils();

// 獲取版本信息（使用與 quasar.config.js 相同的版本號）
const versionInfo = versionUtils.getVersionInfo();

// 确保 dist/pwa 目录存在
const distDir = path.join(__dirname, '../dist/pwa');
if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir, { recursive: true });
}

// 写入版本文件
const versionFilePath = path.join(distDir, 'version.json');
fs.writeFileSync(versionFilePath, JSON.stringify(versionInfo, null, 2));

console.log('版本文件已生成:', versionFilePath);
console.log('版本信息:', versionInfo);
