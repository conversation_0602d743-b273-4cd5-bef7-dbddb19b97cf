# 尾數分析匯出功能完成說明

## 功能概述
已完成 `BatchAnalysisPage.vue` 中尾數分析的匯出功能，參考版路分析的匯出格式，並整合了 `TailFollowResult.vue` 中 tab=1 的預測結果(出現次數)部分。

## 主要修改內容

### 1. 更新匯出邏輯
在 `downloadExcel()` 函數中，將原來的尾數分析匯出：
```javascript
case 'tail':
  createTailSheet(workbook);
  createActualNumbersSheet(workbook);
  break;
```

修改為：
```javascript
case 'tail':
  createTailAnalysisSheet(workbook);
  createTailAppearancesSheet(workbook);
  createActualNumbersSheet(workbook);
  break;
```

### 2. 新增兩個匯出工作表函數

#### A. `createTailAnalysisSheet()` - 尾數分析主要工作表
- 保留原有的尾數分析功能
- 包含：日期、預測尾數、實際尾數、命中尾數、命中數量
- 格式與原來的 `createTailSheet()` 相同

#### B. `createTailAppearancesSheet()` - 尾數分析統計工作表
- **新增功能**：參考 `TailFollowResult.vue` 中 tab=1 的預測結果
- 顯示尾數出現次數統計 (`tailNumAppearances`)
- 按出現次數排序（次數高的在前）
- 每10個尾數一列的格式
- 尾數不使用 padding zero（與版路分析的號碼格式區分）

### 3. 工作表結構

#### 尾數分析統計工作表內容：
- **工作表名稱**：「尾數分析」
- **標題**：「尾數分析統計」
- **格式**：
  - 第一欄：日期
  - 後續欄位：按出現次數排序的尾數（每列最多10個）
  - 列寬設置：日期欄12字符，尾數欄6字符

#### 實際開獎號碼工作表：
- 保持原有功能不變
- 顯示每期的實際開獎號碼

### 4. 數據來源
- 使用 `batchResults.value` 中的 `tailNumAppearances` 數據
- 這個數據來自於尾數分析的詳細統計結果
- 與 `TailFollowResult.vue` 中 `paginatedResults.targetNumAppearances` 的數據結構相同

### 5. 綜合分析更新
同時更新了綜合分析 (`createPatternSheet`) 中的尾數分析部分，確保包含新的統計工作表。

## 匯出結果
當選擇「尾數分析」並執行匯出時，Excel 文件將包含：
1. **參數設定** - 包含所有分析參數和篩選條件
2. **預測尾數** - 統計結果（按出現次數排序的尾數）
3. **實際開獎號碼** - 各期開獎號碼

## 問題修復與優化
- **修復工作表名稱重複問題**：原本兩個工作表都叫「尾數分析」導致 Excel 錯誤
- **刪除不需要的工作表**：移除「尾數分析結果」工作表，只保留「預測尾數」統計
- **簡化匯出結構**：尾數分析只需要統計結果，不需要詳細的分析結果
- **新增篩選條件**：為尾數分析添加「連續拖出次數」篩選功能（最多10次）
- **最終工作表名稱**：
  - 統計結果：「預測尾數」
  - 開獎號碼：「實際開獎號碼」

## 技術特點
- 保持與版路分析匯出格式的一致性
- 尾數格式不使用 padding zero（如：1, 2, 3 而非 01, 02, 03）
- 按出現次數降序排列，符合用戶查看習慣
- **獨立篩選條件**：尾數分析有專用的篩選條件變數 `tailAccuracy` 和 `tailFilterCondition`
- **連續拖出次數範圍**：支援1-10次的連續拖出次數篩選
- 自動設置適當的列寬

## 新增篩選功能詳細說明
### UI 組件
- **連續拖出次數**：下拉選單，選項從「已連續拖出 1 次」到「已連續拖出 10 次」
- **篩選條件**：下拉選單，包含「(含)以上」、「(含)以下」、「剛好」三個選項

### 變數定義
```javascript
// 尾數分析篩選條件
const tailAccuracy = ref(1);
const tailAccuracyOpts = ref([
  { label: '已連續拖出 1 次', value: 1 },
  // ... 到 10 次
]);

const tailFilterCondition = ref('above');
```

### 邏輯應用
- 尾數分析使用 `tailAccuracy.value` 和 `tailFilterCondition.value`
- 版路分析仍使用原有的 `accuracy.value` 和 `filterCondition.value`
- 綜合分析中的尾數部分也使用尾數專用的篩選條件

## 格式說明
### 預測尾數工作表
- **格式**：參考版路分析的預測號碼格式
- **內容**：按出現次數排序的尾數統計
- **每列最多10個尾數**，便於閱讀

## 綜合分析匯出功能
### 新增功能
- **完成綜合分析匯出**：實現了完整的綜合分析 Excel 匯出功能
- **數據結構擴展**：為 `BatchAnalysisResult` 添加了綜合分析專用字段
- **計算邏輯**：實現了 `calculatePatternAnalysisData` 函數，參考 `PatternResult.vue` 的邏輯

### 綜合分析匯出內容（已優化）
當選擇「綜合分析」並執行匯出時，Excel 文件將包含 **11個工作表**，按以下順序排列：

**參數設定工作表：**
1. **參數設定** - 包含所有分析參數、篩選條件和分析資訊

**綜合分析專用工作表（第2-7個）：**
2. **版路分析尾數** - 版路分析的尾數計算結果
3. **尾數分析尾數** - 尾數分析的尾數計算結果
4. **綜合分析尾數** - 綜合分析的尾數計算結果
5. **版路+尾數比對** - 版路分析與尾數分析的比對結果
6. **版路+綜合比對** - 版路分析與綜合分析的比對結果
7. **尾數+綜合比對** - 尾數分析與綜合分析的比對結果

**版路分析工作表（帶標記）：**
8. **版路分析-預測號碼** - 版路分析預測號碼統計
9. **版路分析-未出現號碼-預測次數** - 版路分析未出現號碼（按預測次數排序）
10. **版路分析-未出現號碼-大小排序** - 版路分析未出現號碼（按大小排序）
11. **版路分析-尾數統計** - 版路分析尾數統計

**尾數分析工作表（帶標記）：**
12. **尾數分析-預測尾數** - 尾數分析尾數統計

**通用工作表：**
13. **實際開獎號碼** - 各期開獎號碼

### 技術實現
- **數據計算**：實現了版路分析尾數、尾數分析尾數、綜合分析尾數的計算
- **比對邏輯**：實現了三種比對組合的計算邏輯
- **獨立篩選條件**：綜合分析中版路和尾數分析各自有獨立的篩選條件
- **排序功能**：所有尾數按照分數/次數降序排列，0排在最後
- **工作表結構**：清晰的工作表分類，便於用戶查看和分析

### 綜合分析篩選條件
- **版路分析篩選條件**：`patternBallAccuracy` 和 `patternBallFilterCondition`
- **尾數分析篩選條件**：`patternTailAccuracy` 和 `patternTailFilterCondition`
- **UI組件**：分別顯示「版路分析篩選條件」和「尾數分析篩選條件」兩個區塊
- **邏輯應用**：在 `extractDetailedAnalysis` 和 `calculatePatternAnalysisData` 中使用對應的篩選條件

## 綜合分析優化功能
### 問題修復
1. **獨立分頁顯示**：將「綜合分析尾數」和「綜合比對結果」拆分為6個獨立工作表
2. **獨立cell格式**：每個數字都在獨立的cell中，不再用空白間隔
3. **★標記功能**：準確預測的尾數會自動添加★標記
4. **工作表順序優化**：綜合分析專用工作表移到最前方
5. **分析標記**：版路和尾數分析的工作表添加前綴標記，便於識別

### 技術實現亮點
- **`applyTailStarMarks` 函數**：專門為尾數添加★標記
- **獨立工作表函數**：每種分析結果都有專門的創建函數
- **清晰的命名規則**：工作表名稱包含分析類型前綴
- **數據格式統一**：所有工作表都使用相同的格式（日期+每10個數字一列）

## 參數設定工作表功能
### 新增功能
- **完整參數記錄**：為所有三種分析方式添加參數設定工作表
- **統一格式**：所有分析方式的參數設定都使用相同的格式
- **詳細資訊**：包含基本設定、分析參數、篩選條件和分析資訊

### 參數設定內容
#### 版路分析參數設定
- **基本設定**：彩票類型、分析方法
- **版路分析參數**：拖牌組合、推算期數、拖牌區間、預測期數
- **篩選條件**：連續拖出次數、篩選條件
- **分析資訊**：分析時間、分析期數

#### 尾數分析參數設定
- **基本設定**：彩票類型、分析方法
- **尾數分析參數**：拖牌組合、推算期數、拖牌區間、預測期數
- **篩選條件**：連續拖出次數、篩選條件
- **分析資訊**：分析時間、分析期數

#### 綜合分析參數設定
- **基本設定**：彩票類型、分析方法
- **版路分析參數**：拖牌組合、推算期數、拖牌區間
- **尾數分析參數**：拖牌組合、推算期數、拖牌區間
- **預測期數**：綜合分析的預測期數
- **版路分析篩選條件**：連續拖出次數、篩選條件
- **尾數分析篩選條件**：連續拖出次數、篩選條件
- **分析資訊**：分析時間、分析期數

### 技術實現
- **`createParametersSheet` 函數**：統一的參數設定工作表創建函數
- **動態參數讀取**：根據分析類型動態讀取對應的參數
- **格式化顯示**：拖牌組合以「1-2-3」格式顯示，篩選條件顯示中文標籤
- **樣式設置**：標題行加粗，適當的列寬設置

## 測試建議
### 尾數分析測試
1. 選擇尾數分析方法
2. 設置分析參數並執行分析
3. 點擊下載按鈕
4. 檢查生成的 Excel 文件是否包含兩個尾數相關的工作表
5. 驗證數據格式和排序是否正確

### 綜合分析測試
1. 選擇綜合分析方法
2. 設置版路分析和尾數分析參數
3. 執行分析並下載
4. 檢查 Excel 文件是否包含13個工作表
5. 驗證參數設定工作表是否包含所有設定資訊
6. 驗證綜合分析尾數和比對結果的計算是否正確

### 參數設定測試
1. 修改各種參數設定（拖牌組合、期數、篩選條件等）
2. 執行分析並下載Excel
3. 檢查參數設定工作表是否正確記錄所有參數
4. 驗證參數格式是否正確（如拖牌組合顯示為「1-2-3」格式）
5. 確認分析時間和期數資訊是否正確
