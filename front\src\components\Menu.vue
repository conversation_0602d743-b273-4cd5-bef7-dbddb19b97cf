<template>
  <template v-if="!requireAdmin || authStore.isAdmin()">
    <q-item clickable :to="link" @click="toggleMenu" v-if="link">
      <q-item-section v-if="icon" avatar>
        <q-icon :name="icon" />
      </q-item-section>

      <q-item-section>
        <q-item-label>{{ title }}</q-item-label>
      </q-item-section>
    </q-item>

    <q-item clickable @click="onClick" v-else>
      <q-item-section v-if="icon" avatar>
        <q-icon :name="icon" />
      </q-item-section>

      <q-item-section>
        <q-item-label>{{ title }}</q-item-label>
      </q-item-section>
    </q-item>
  </template>
</template>

<script setup lang="ts">
import { useAuthStore } from 'src/stores/auth';

const authStore = useAuthStore();

defineOptions({
  name: 'MenuLink',
});

const emit = defineEmits(['toggleMenu']);

export interface MenuLinkProps {
  title: string;
  link?: string;
  onClick?: () => void;
  icon?: string;
  requireAdmin?: boolean;
}

withDefaults(defineProps<MenuLinkProps>(), {
  link: '',
  icon: '',
  requireAdmin: false,
});

const toggleMenu = () => {
  emit('toggleMenu');
};
</script>
